from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from loguru import logger
from app.core.config import settings

router = APIRouter()

class QueryRequest(BaseModel):
    question: str

class QueryResponse(BaseModel):
    question: str
    results: list
    status: str

@router.post("/query_askhr", response_model=QueryResponse)
async def query_askhr(payload: QueryRequest):
    """Query askhr_collection with the provided question"""
    try:
        # Import and create instance here to avoid startup failures
        from app.services.milvus_service import MilvusService
        milvus_service = MilvusService(collection_name=settings.MILVUS_ASKHR_COLLECTION)

        # Execute query
        results = milvus_service.query(payload.question)

        logger.info(f"AskHR query processed successfully for question: {payload.question[:50]}...")

        return QueryResponse(
            question=payload.question,
            results=results,
            status="success"
        )

    except ConnectionError as e:
        logger.error(f"Milvus connection error: {e}")
        raise HTTPException(
            status_code=503,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"AskHR query failed: {e}")
        raise HTTPException(status_code=500, detail=f"AskHR query failed: {str(e)}")

@router.post("/query_askit", response_model=QueryResponse)
async def query_askit(payload: QueryRequest):
    """Query askit_collection with the provided question"""
    try:
        # Import and create instance here to avoid startup failures
        from app.services.milvus_service import MilvusService
        milvus_service = MilvusService(collection_name=settings.MILVUS_ASKIT_COLLECTION)

        # Execute query
        results = milvus_service.query(payload.question)

        logger.info(f"AskIT query processed successfully for question: {payload.question[:50]}...")

        return QueryResponse(
            question=payload.question,
            results=results,
            status="success"
        )

    except ConnectionError as e:
        logger.error(f"Milvus connection error: {e}")
        raise HTTPException(
            status_code=503,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"AskIT query failed: {e}")
        raise HTTPException(status_code=500, detail=f"AskIT query failed: {str(e)}")
