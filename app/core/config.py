import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from config.env
# Try multiple paths to find config.env
config_paths = [
    "config.env",  # Current directory
    "../config.env",  # Parent directory
    "../../config.env",  # Two levels up
]

for config_path in config_paths:
    if Path(config_path).exists():
        load_dotenv(config_path)
        break
else:
    # Fallback to default load_dotenv
    load_dotenv()

class Settings:
    def __init__(self):
        # App Configuration
        self.APP_NAME: str = os.getenv("APP_NAME", "warbyparker-retrival")
        self.APP_ENV: str = os.getenv("APP_ENV", "development")
        self.APP_HOST: str = os.getenv("APP_HOST", "0.0.0.0")
        # IBM Code Engine uses PORT env var, fallback to APP_PORT or 8000
        self.APP_PORT: int = int(os.getenv("PORT", os.getenv("APP_PORT", "8000")))

        # Logging
        self.LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

        # IBM Cloud Configuration
        self.API_KEY: str = os.getenv("API_KEY", "")
        self.IBM_CLOUD_URL: str = os.getenv("IBM_CLOUD_URL", "")
        self.PROJECT_ID: str = os.getenv("PROJECT_ID", "")
        self.ACCESS_TOKEN: str = os.getenv("ACCESS_TOKEN", "")

        # Milvus Configuration
        self.MILVUS_HOST: str = os.getenv("MILVUS_HOST", "")
        self.MILVUS_PORT: str = os.getenv("MILVUS_PORT", "")
        self.MILVUS_USER: str = os.getenv("MILVUS_USER", "")
        self.MILVUS_PASSWORD: str = os.getenv("MILVUS_PASSWORD", "")
        self.MILVUS_COLLECTION: str = os.getenv("MILVUS_COLLECTION", "")
        self.MILVUS_ASKHR_COLLECTION: str = os.getenv("MILVUS_ASKHR_COLLECTION", "")
        self.MILVUS_ASKIT_COLLECTION: str = os.getenv("MILVUS_ASKIT_COLLECTION", "")
        # Certificate path - check multiple locations for container/local compatibility
        self.MILVUS_CERT: str = os.getenv("MILVUS_CERT", "app/config/cert.crt")

        # Validate and initialize settings
        self._validate_settings()

    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        if not self.APP_ENV:
            return False
        return self.APP_ENV.lower() in ["production", "prod"]

    @property
    def is_code_engine(self) -> bool:
        """Check if running in IBM Code Engine"""
        return os.getenv("CE_DOMAIN") is not None or os.getenv("K_SERVICE") is not None

    def _validate_settings(self):
        """Validate required configuration"""
        # Validate required Milvus settings
        if not self.MILVUS_HOST or not self.MILVUS_PORT:
            raise ValueError("Milvus connection details (MILVUS_HOST, MILVUS_PORT) are required")

        # Validate that at least one collection is specified
        if not self.MILVUS_ASKHR_COLLECTION and not self.MILVUS_ASKIT_COLLECTION:
            raise ValueError("At least one Milvus collection (MILVUS_ASKHR_COLLECTION or MILVUS_ASKIT_COLLECTION) must be specified")

        # Check if certificate file exists - only validate in non-container environments
        # In containers, the cert might be mounted or provided differently
        if not self.is_code_engine:
            cert_path = Path(self.MILVUS_CERT)
            if not cert_path.exists():
                # Try alternative paths
                alt_paths = [
                    "/app/config/cert.crt",
                    "/config/cert.crt",
                    "config/cert.crt",
                    "cert.crt"
                ]
                found = False
                for alt_path in alt_paths:
                    if Path(alt_path).exists():
                        self.MILVUS_CERT = alt_path
                        found = True
                        break

                if not found:
                    # In development, warn but don't fail
                    if not self.is_production:
                        print(f"Warning: Milvus certificate file not found. Tried: {self.MILVUS_CERT}, {', '.join(alt_paths)}")
                    else:
                        raise FileNotFoundError(f"Milvus certificate file not found. Tried: {self.MILVUS_CERT}, {', '.join(alt_paths)}")

        # Create logs directory if it doesn't exist
        self._ensure_logs_directory()

    def _ensure_logs_directory(self):
        """Ensure logs directory exists"""
        logs_dir = Path("logs")
        try:
            logs_dir.mkdir(exist_ok=True)
        except PermissionError:
            print(f"Warning: Cannot create logs directory at {logs_dir.absolute()}. Logging to file may fail.")
        except Exception as e:
            print(f"Warning: Error creating logs directory: {e}")

settings = Settings()
