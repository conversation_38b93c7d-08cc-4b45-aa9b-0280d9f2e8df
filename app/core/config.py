import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from config.env
# Try multiple paths to find config.env
config_paths = [
    "config.env",  # Current directory
    "../config.env",  # Parent directory
    "../../config.env",  # Two levels up
]

for config_path in config_paths:
    if Path(config_path).exists():
        load_dotenv(config_path)
        break
else:
    # Fallback to default load_dotenv
    load_dotenv()

class Settings:
    # App Configuration
    APP_NAME: str = os.getenv("APP_NAME", "warbyparker-retrival")
    APP_ENV: str = os.getenv("APP_ENV", "development")
    APP_HOST: str = os.getenv("APP_HOST", "0.0.0.0")
    # IBM Code Engine uses PORT env var, fallback to APP_PORT or 8000
    APP_PORT: int = int(os.getenv("PORT", os.getenv("APP_PORT", 8000)))

    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # IBM Cloud Configuration
    API_KEY: str = os.getenv("API_KEY", "")
    IBM_CLOUD_URL: str = os.getenv("IBM_CLOUD_URL", "")
    PROJECT_ID: str = os.getenv("PROJECT_ID", "")
    ACCESS_TOKEN: str = os.getenv("ACCESS_TOKEN", "")

    # Milvus Configuration
    MILVUS_HOST: str = os.getenv("MILVUS_HOST", "")
    MILVUS_PORT: str = os.getenv("MILVUS_PORT", "")
    MILVUS_USER: str = os.getenv("MILVUS_USER", "")
    MILVUS_PASSWORD: str = os.getenv("MILVUS_PASSWORD", "")
    MILVUS_COLLECTION: str = os.getenv("MILVUS_COLLECTION", "")
    MILVUS_ASKHR_COLLECTION: str = os.getenv("MILVUS_ASKHR_COLLECTION", "")
    MILVUS_ASKIT_COLLECTION: str = os.getenv("MILVUS_ASKIT_COLLECTION", "")
    # Certificate path - check multiple locations for container/local compatibility
    MILVUS_CERT: str = os.getenv("MILVUS_CERT", "app/config/cert.crt")

    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.APP_ENV.lower() in ["production", "prod"]

    @property
    def is_code_engine(self) -> bool:
        """Check if running in IBM Code Engine"""
        return os.getenv("CE_DOMAIN") is not None or os.getenv("K_SERVICE") is not None

    def __post_init__(self):
        """Validate required configuration"""
        if not self.MILVUS_HOST or not self.MILVUS_PORT:
            raise ValueError("Milvus connection details (MILVUS_HOST, MILVUS_PORT) are required")

        # Check if certificate file exists - only validate in non-container environments
        # In containers, the cert might be mounted or provided differently
        if not self.is_code_engine:
            cert_path = Path(self.MILVUS_CERT)
            if not cert_path.exists():
                # Try alternative paths
                alt_paths = [
                    "/app/config/cert.crt",
                    "/config/cert.crt",
                    "config/cert.crt",
                    "cert.crt"
                ]
                found = False
                for alt_path in alt_paths:
                    if Path(alt_path).exists():
                        self.MILVUS_CERT = alt_path
                        found = True
                        break

                if not found:
                    raise FileNotFoundError(f"Milvus certificate file not found. Tried: {self.MILVUS_CERT}, {', '.join(alt_paths)}")

settings = Settings()
# Validate settings on import
settings.__post_init__()
