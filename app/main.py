from fastapi import <PERSON><PERSON><PERSON>
from app.api.v1.endpoints import health, query
from app.core.exceptions import http_exception_handler, generic_exception_handler
from app.core.config import settings
from fastapi.exceptions import HTTPException
from loguru import logger
import sys
from pathlib import Path

# Configure logging based on settings
logger.remove()
logger.add(
    sys.stdout,
    level=settings.LOG_LEVEL,
    format="<green>{time}</green> | <level>{level}</level> | <cyan>{message}</cyan>"
)

# Add file logging with error handling
try:
    # Ensure logs directory exists (already handled in settings, but double-check)
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    logger.add(
        "logs/app.log",
        rotation="1 week",
        level="DEBUG",
        backtrace=True,
        diagnose=True
    )
    logger.info("File logging enabled")
except Exception as e:
    logger.warning(f"Could not enable file logging: {e}. Continuing with console logging only.")

app = FastAPI(
    title="Warby Parker Retrieval API",
    description="FastAPI application with Milvus integration for document retrieval",
    version="1.0.0"
)

# Routers
app.include_router(health.router, prefix="/api/v1", tags=["Health"])
app.include_router(query.router, prefix="/api/v1", tags=["Query"])

# Exception handlers
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, generic_exception_handler)

@app.on_event("startup")
async def startup_event():
    logger.info("Warby Parker Retrieval API starting...")
    logger.info(f"Environment: {settings.APP_ENV}")
    logger.info(f"Milvus Host: {settings.MILVUS_HOST}:{settings.MILVUS_PORT}")
    logger.info(f"AskHR Collection: {settings.MILVUS_ASKHR_COLLECTION}")
    logger.info(f"AskIT Collection: {settings.MILVUS_ASKIT_COLLECTION}")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Warby Parker Retrieval API shutting down...")
