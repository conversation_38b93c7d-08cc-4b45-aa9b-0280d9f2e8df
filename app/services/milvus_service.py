from pymilvus import connections, Collection, utility
from app.core.config import settings
from loguru import logger
from pathlib import Path

class MilvusService:
    def __init__(self, collection_name=None):
        self.collection = None
        self.collection_name = collection_name
        self.is_connected = False
        self.collection_exists = False
        # Don't connect immediately during import to avoid startup failures

    def connect_milvus(self):
        """Connect to Milvus with SSL and authentication"""
        try:
            # Validate certificate file exists
            cert_path = Path(settings.MILVUS_CERT)
            if not cert_path.exists():
                raise FileNotFoundError(f"Certificate file not found: {settings.MILVUS_CERT}")

            # Connect with SSL and authentication
            connections.connect(
                alias="default",
                host=settings.MILVUS_HOST,
                port=settings.MILVUS_PORT,
                user=settings.MILVUS_USER,
                password=settings.MILVUS_PASSWORD,
                secure=True,
                server_pem_path=settings.MILVUS_CERT
            )

            # Get server version to verify connection
            version = utility.get_server_version()
            logger.info(f"Connected to <PERSON><PERSON>vus (version {version})")

            # Check if collection exists
            collection_to_check = self.collection_name or settings.MILVUS_COLLECTION
            if utility.has_collection(collection_to_check):
                self.collection = Collection(collection_to_check)
                self.collection_exists = True
                logger.info(f"Connected to Milvus collection: {collection_to_check}")
            else:
                self.collection_exists = False
                logger.warning(f"Collection '{collection_to_check}' does not exist")

            self.is_connected = True

        except Exception as e:
            logger.error(f"Failed to connect to Milvus: {e}")
            self.is_connected = False
            self.collection_exists = False
            # Don't raise the exception to allow the app to start
            # raise

    def health_check(self) -> dict:
        """Check Milvus connection health"""
        try:
            # Try to connect if not connected
            if not self.is_connected:
                self.connect_milvus()

            if not self.is_connected:
                return {"status": "disconnected", "error": "Failed to connect to Milvus"}

            # Check collection status
            collection_to_check = self.collection_name or settings.MILVUS_COLLECTION
            if not self.collection_exists:
                return {
                    "status": "warning",
                    "collection": collection_to_check,
                    "error": f"Collection '{collection_to_check}' does not exist",
                    "message": "Collection needs to be created and populated with data"
                }

            # Try to get collection info
            if self.collection:
                num_entities = self.collection.num_entities
                return {
                    "status": "OK",
                    "collection": collection_to_check,
                    "num_entities": num_entities
                }
            else:
                return {"status": "error", "error": "Collection not initialized"}

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"status": "error", "error": str(e)}

    def query(self, question: str):
        """Query Milvus collection using vector similarity search"""
        # Try to connect if not connected
        if not self.is_connected:
            self.connect_milvus()

        if not self.is_connected:
            raise ConnectionError("Failed to connect to Milvus")

        collection_to_check = self.collection_name or settings.MILVUS_COLLECTION
        if not self.collection_exists:
            raise ConnectionError(f"Collection '{collection_to_check}' does not exist. Please create and populate the collection first.")

        if not self.collection:
            raise ConnectionError("Collection not initialized")

        try:
            # Import here to avoid circular imports
            from sentence_transformers import SentenceTransformer

            # Initialize embedding model (same as used during ingestion)
            embed_model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")

            # Generate query embedding
            query_vector = embed_model.encode(question).tolist()

            # Load collection for search
            self.collection.load()

            # Perform vector similarity search
            search_results = self.collection.search(
                data=[query_vector],
                anns_field="vector",
                param={"metric_type": "L2", "params": {"nprobe": 10}},
                limit=5,
                output_fields=["chunk_text", "file_name_original", "page_number", "chunk_id", "mime_type", "modified_time", "last_modified_by", "agent", "web_url"]
            )

            # Format results
            formatted_results = []
            for result in search_results[0]:
                formatted_results.append({
                    "score": float(result.score),
                    "chunk_text": result.entity.get('chunk_text'),
                    "file_name_original": result.entity.get('file_name_original'),
                    "page_number": result.entity.get('page_number'),
                    "chunk_id": result.entity.get('chunk_id'),
                    "mime_type": result.entity.get('mime_type'),
                    "modified_time": result.entity.get('modified_time'),
                    "last_modified_by": result.entity.get('last_modified_by'),
                    "agent": result.entity.get('agent'),
                    "web_url": result.entity.get('web_url')
                })

            logger.info(f"Query executed successfully, returned {len(formatted_results)} results")
            return formatted_results

        except Exception as e:
            logger.error(f"Query failed: {e}")
            raise

    def disconnect(self):
        """Disconnect from Milvus"""
        try:
            connections.disconnect("default")
            self.is_connected = False
            logger.info("Disconnected from Milvus")
        except Exception as e:
            logger.error(f"Error disconnecting from Milvus: {e}")
