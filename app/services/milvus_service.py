from pymilvus import connections, Collection, utility
from app.core.config import settings
from loguru import logger
from pathlib import Path
import time
import os

class MilvusService:
    def __init__(self, collection_name=None):
        self.collection = None
        self.collection_name = collection_name
        self.is_connected = False
        self.collection_exists = False
        self.embed_model = None
        self.connection_retries = 3
        self.retry_delay = 2  # seconds
        # Don't connect immediately during import to avoid startup failures

    def connect_milvus(self):
        """Connect to Milvus with SSL and authentication with retry logic"""
        for attempt in range(self.connection_retries):
            try:
                # Validate certificate file exists
                cert_path = Path(settings.MILVUS_CERT)
                if not cert_path.exists():
                    raise FileNotFoundError(f"Certificate file not found: {settings.MILVUS_CERT}")

                # Disconnect any existing connection first
                try:
                    connections.disconnect("default")
                except Exception:
                    pass  # Ignore if no connection exists

                # Connect with SSL and authentication
                connections.connect(
                    alias="default",
                    host=settings.MILVUS_HOST,
                    port=settings.MILVUS_PORT,
                    user=settings.MILVUS_USER,
                    password=settings.MILVUS_PASSWORD,
                    secure=True,
                    server_pem_path=settings.MILVUS_CERT
                )

                # Get server version to verify connection
                version = utility.get_server_version()
                logger.info(f"Connected to Milvus (version {version})")

                # Check if collection exists
                collection_to_check = self.collection_name or settings.MILVUS_COLLECTION
                if not collection_to_check:
                    raise ValueError("No collection name specified")

                if utility.has_collection(collection_to_check):
                    self.collection = Collection(collection_to_check)
                    self.collection_exists = True
                    logger.info(f"Connected to Milvus collection: {collection_to_check}")

                    # Validate collection schema
                    self._validate_collection_schema()
                else:
                    self.collection_exists = False
                    logger.warning(f"Collection '{collection_to_check}' does not exist")

                self.is_connected = True
                return  # Success, exit retry loop

            except Exception as e:
                logger.error(f"Failed to connect to Milvus (attempt {attempt + 1}/{self.connection_retries}): {e}")
                self.is_connected = False
                self.collection_exists = False

                if attempt < self.connection_retries - 1:
                    logger.info(f"Retrying connection in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error("All connection attempts failed")
                    # Don't raise the exception to allow the app to start

    def _validate_collection_schema(self):
        """Validate that the collection has the expected schema"""
        if not self.collection:
            return

        try:
            # Get collection schema
            schema = self.collection.schema
            field_names = [field.name for field in schema.fields]

            # Expected fields for the search query
            expected_fields = [
                "vector", "chunk_text", "file_name_original", "page_number",
                "chunk_id", "mime_type", "modified_time", "last_modified_by",
                "agent", "web_url"
            ]

            missing_fields = [field for field in expected_fields if field not in field_names]
            if missing_fields:
                logger.warning(f"Collection schema missing expected fields: {missing_fields}")

        except Exception as e:
            logger.warning(f"Could not validate collection schema: {e}")

    def _initialize_embedding_model(self):
        """Initialize the sentence transformer model with error handling"""
        if self.embed_model is not None:
            return self.embed_model

        try:
            # Import here to avoid circular imports
            from sentence_transformers import SentenceTransformer

            # Check if model cache directory exists and is writable
            cache_dir = os.path.expanduser("~/.cache/torch/sentence_transformers")
            os.makedirs(cache_dir, exist_ok=True)

            logger.info("Loading sentence transformer model...")
            # Initialize embedding model (same as used during ingestion)
            self.embed_model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
            logger.info("Sentence transformer model loaded successfully")
            return self.embed_model

        except ImportError as e:
            logger.error(f"sentence-transformers package not available: {e}")
            raise ImportError("sentence-transformers package is required but not installed")
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            raise RuntimeError(f"Could not initialize embedding model: {e}")

    def health_check(self) -> dict:
        """Check Milvus connection health"""
        try:
            # Try to connect if not connected
            if not self.is_connected:
                self.connect_milvus()

            if not self.is_connected:
                return {"status": "disconnected", "error": "Failed to connect to Milvus"}

            # Check collection status
            collection_to_check = self.collection_name or settings.MILVUS_COLLECTION
            if not collection_to_check:
                return {"status": "error", "error": "No collection name specified"}

            if not self.collection_exists:
                return {
                    "status": "warning",
                    "collection": collection_to_check,
                    "error": f"Collection '{collection_to_check}' does not exist",
                    "message": "Collection needs to be created and populated with data"
                }

            # Try to get collection info
            if self.collection:
                try:
                    # Load collection to get accurate count
                    self.collection.load()
                    num_entities = self.collection.num_entities

                    # Check if embedding model can be loaded
                    try:
                        self._initialize_embedding_model()
                        model_status = "OK"
                    except Exception as model_error:
                        model_status = f"Error: {str(model_error)}"

                    return {
                        "status": "OK",
                        "collection": collection_to_check,
                        "num_entities": num_entities,
                        "embedding_model": model_status
                    }
                except Exception as collection_error:
                    logger.error(f"Error accessing collection: {collection_error}")
                    return {
                        "status": "error",
                        "collection": collection_to_check,
                        "error": f"Collection access error: {str(collection_error)}"
                    }
            else:
                return {"status": "error", "error": "Collection not initialized"}

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"status": "error", "error": str(e)}

    def query(self, question: str):
        """Query Milvus collection using vector similarity search"""
        # Validate input
        if not question or not question.strip():
            raise ValueError("Question cannot be empty")

        # Try to connect if not connected
        if not self.is_connected:
            self.connect_milvus()

        if not self.is_connected:
            raise ConnectionError("Failed to connect to Milvus")

        collection_to_check = self.collection_name or settings.MILVUS_COLLECTION
        if not collection_to_check:
            raise ValueError("No collection name specified")

        if not self.collection_exists:
            raise ConnectionError(f"Collection '{collection_to_check}' does not exist. Please create and populate the collection first.")

        if not self.collection:
            raise ConnectionError("Collection not initialized")

        try:
            # Initialize embedding model with error handling
            embed_model = self._initialize_embedding_model()

            # Generate query embedding
            logger.debug(f"Generating embedding for query: {question[:100]}...")
            query_vector = embed_model.encode(question.strip()).tolist()

            if not query_vector:
                raise ValueError("Failed to generate query embedding")

            # Load collection for search
            logger.debug("Loading collection for search...")
            self.collection.load()

            # Perform vector similarity search with error handling
            logger.debug("Performing vector similarity search...")

            # Try COSINE first (most common), then fall back to L2 if it fails
            search_params = [
                {"metric_type": "COSINE", "params": {"nprobe": 10}},
                {"metric_type": "L2", "params": {"nprobe": 10}},
                {"metric_type": "IP", "params": {"nprobe": 10}}  # Inner Product
            ]

            search_results = None
            last_error = None

            for param in search_params:
                try:
                    search_results = self.collection.search(
                        data=[query_vector],
                        anns_field="vector",
                        param=param,
                        limit=5,
                        output_fields=["chunk_text", "file_name_original", "page_number", "chunk_id", "mime_type", "modified_time", "last_modified_by", "agent", "web_url"]
                    )
                    logger.debug(f"Search successful with metric type: {param['metric_type']}")
                    break
                except Exception as e:
                    last_error = e
                    logger.debug(f"Search failed with {param['metric_type']}: {e}")
                    continue

            if search_results is None:
                raise RuntimeError(f"All search metric types failed. Last error: {last_error}")

            # Validate search results
            if not search_results or len(search_results) == 0:
                logger.warning("No search results returned")
                return []

            # Format results with error handling
            formatted_results = []
            try:
                for result in search_results[0]:
                    try:
                        formatted_result = {
                            "score": float(result.score) if hasattr(result, 'score') else 0.0,
                            "chunk_text": result.entity.get('chunk_text', '') if result.entity else '',
                            "file_name_original": result.entity.get('file_name_original', '') if result.entity else '',
                            "page_number": result.entity.get('page_number', 0) if result.entity else 0,
                            "chunk_id": result.entity.get('chunk_id', '') if result.entity else '',
                            "mime_type": result.entity.get('mime_type', '') if result.entity else '',
                            "modified_time": result.entity.get('modified_time', '') if result.entity else '',
                            "last_modified_by": result.entity.get('last_modified_by', '') if result.entity else '',
                            "agent": result.entity.get('agent', '') if result.entity else '',
                            "web_url": result.entity.get('web_url', '') if result.entity else ''
                        }
                        formatted_results.append(formatted_result)
                    except Exception as format_error:
                        logger.warning(f"Error formatting search result: {format_error}")
                        continue

                logger.info(f"Query executed successfully, returned {len(formatted_results)} results")
                return formatted_results

            except Exception as results_error:
                logger.error(f"Error processing search results: {results_error}")
                return []

        except ValueError as ve:
            logger.error(f"Query validation error: {ve}")
            raise
        except ConnectionError as ce:
            logger.error(f"Connection error during query: {ce}")
            raise
        except Exception as e:
            logger.error(f"Query failed: {e}")
            raise RuntimeError(f"Query execution failed: {str(e)}")

    def disconnect(self):
        """Disconnect from Milvus"""
        try:
            if self.is_connected:
                connections.disconnect("default")
                self.is_connected = False
                self.collection_exists = False
                self.collection = None
                logger.info("Disconnected from Milvus")
        except Exception as e:
            logger.error(f"Error disconnecting from Milvus: {e}")

    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            self.disconnect()
        except Exception:
            pass  # Ignore errors during cleanup
