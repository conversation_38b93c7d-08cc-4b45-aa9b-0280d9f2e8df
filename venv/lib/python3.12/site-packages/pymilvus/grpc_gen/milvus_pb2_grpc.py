# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import common_pb2 as common__pb2
from . import feder_pb2 as feder__pb2
from . import milvus_pb2 as milvus__pb2

GRPC_GENERATED_VERSION = '1.66.2'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in milvus_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class MilvusServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateCollection = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreateCollection',
                request_serializer=milvus__pb2.CreateCollectionRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DropCollection = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DropCollection',
                request_serializer=milvus__pb2.DropCollectionRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.HasCollection = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/HasCollection',
                request_serializer=milvus__pb2.HasCollectionRequest.SerializeToString,
                response_deserializer=milvus__pb2.BoolResponse.FromString,
                _registered_method=True)
        self.LoadCollection = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/LoadCollection',
                request_serializer=milvus__pb2.LoadCollectionRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.ReleaseCollection = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ReleaseCollection',
                request_serializer=milvus__pb2.ReleaseCollectionRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DescribeCollection = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DescribeCollection',
                request_serializer=milvus__pb2.DescribeCollectionRequest.SerializeToString,
                response_deserializer=milvus__pb2.DescribeCollectionResponse.FromString,
                _registered_method=True)
        self.GetCollectionStatistics = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetCollectionStatistics',
                request_serializer=milvus__pb2.GetCollectionStatisticsRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetCollectionStatisticsResponse.FromString,
                _registered_method=True)
        self.ShowCollections = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ShowCollections',
                request_serializer=milvus__pb2.ShowCollectionsRequest.SerializeToString,
                response_deserializer=milvus__pb2.ShowCollectionsResponse.FromString,
                _registered_method=True)
        self.AlterCollection = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/AlterCollection',
                request_serializer=milvus__pb2.AlterCollectionRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.AlterCollectionField = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/AlterCollectionField',
                request_serializer=milvus__pb2.AlterCollectionFieldRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.CreatePartition = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreatePartition',
                request_serializer=milvus__pb2.CreatePartitionRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DropPartition = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DropPartition',
                request_serializer=milvus__pb2.DropPartitionRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.HasPartition = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/HasPartition',
                request_serializer=milvus__pb2.HasPartitionRequest.SerializeToString,
                response_deserializer=milvus__pb2.BoolResponse.FromString,
                _registered_method=True)
        self.LoadPartitions = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/LoadPartitions',
                request_serializer=milvus__pb2.LoadPartitionsRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.ReleasePartitions = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ReleasePartitions',
                request_serializer=milvus__pb2.ReleasePartitionsRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.GetPartitionStatistics = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetPartitionStatistics',
                request_serializer=milvus__pb2.GetPartitionStatisticsRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetPartitionStatisticsResponse.FromString,
                _registered_method=True)
        self.ShowPartitions = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ShowPartitions',
                request_serializer=milvus__pb2.ShowPartitionsRequest.SerializeToString,
                response_deserializer=milvus__pb2.ShowPartitionsResponse.FromString,
                _registered_method=True)
        self.GetLoadingProgress = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetLoadingProgress',
                request_serializer=milvus__pb2.GetLoadingProgressRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetLoadingProgressResponse.FromString,
                _registered_method=True)
        self.GetLoadState = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetLoadState',
                request_serializer=milvus__pb2.GetLoadStateRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetLoadStateResponse.FromString,
                _registered_method=True)
        self.CreateAlias = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreateAlias',
                request_serializer=milvus__pb2.CreateAliasRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DropAlias = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DropAlias',
                request_serializer=milvus__pb2.DropAliasRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.AlterAlias = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/AlterAlias',
                request_serializer=milvus__pb2.AlterAliasRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DescribeAlias = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DescribeAlias',
                request_serializer=milvus__pb2.DescribeAliasRequest.SerializeToString,
                response_deserializer=milvus__pb2.DescribeAliasResponse.FromString,
                _registered_method=True)
        self.ListAliases = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ListAliases',
                request_serializer=milvus__pb2.ListAliasesRequest.SerializeToString,
                response_deserializer=milvus__pb2.ListAliasesResponse.FromString,
                _registered_method=True)
        self.CreateIndex = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreateIndex',
                request_serializer=milvus__pb2.CreateIndexRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.AlterIndex = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/AlterIndex',
                request_serializer=milvus__pb2.AlterIndexRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DescribeIndex = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DescribeIndex',
                request_serializer=milvus__pb2.DescribeIndexRequest.SerializeToString,
                response_deserializer=milvus__pb2.DescribeIndexResponse.FromString,
                _registered_method=True)
        self.GetIndexStatistics = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetIndexStatistics',
                request_serializer=milvus__pb2.GetIndexStatisticsRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetIndexStatisticsResponse.FromString,
                _registered_method=True)
        self.GetIndexState = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetIndexState',
                request_serializer=milvus__pb2.GetIndexStateRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetIndexStateResponse.FromString,
                _registered_method=True)
        self.GetIndexBuildProgress = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetIndexBuildProgress',
                request_serializer=milvus__pb2.GetIndexBuildProgressRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetIndexBuildProgressResponse.FromString,
                _registered_method=True)
        self.DropIndex = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DropIndex',
                request_serializer=milvus__pb2.DropIndexRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.Insert = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/Insert',
                request_serializer=milvus__pb2.InsertRequest.SerializeToString,
                response_deserializer=milvus__pb2.MutationResult.FromString,
                _registered_method=True)
        self.Delete = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/Delete',
                request_serializer=milvus__pb2.DeleteRequest.SerializeToString,
                response_deserializer=milvus__pb2.MutationResult.FromString,
                _registered_method=True)
        self.Upsert = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/Upsert',
                request_serializer=milvus__pb2.UpsertRequest.SerializeToString,
                response_deserializer=milvus__pb2.MutationResult.FromString,
                _registered_method=True)
        self.Search = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/Search',
                request_serializer=milvus__pb2.SearchRequest.SerializeToString,
                response_deserializer=milvus__pb2.SearchResults.FromString,
                _registered_method=True)
        self.HybridSearch = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/HybridSearch',
                request_serializer=milvus__pb2.HybridSearchRequest.SerializeToString,
                response_deserializer=milvus__pb2.SearchResults.FromString,
                _registered_method=True)
        self.Flush = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/Flush',
                request_serializer=milvus__pb2.FlushRequest.SerializeToString,
                response_deserializer=milvus__pb2.FlushResponse.FromString,
                _registered_method=True)
        self.Query = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/Query',
                request_serializer=milvus__pb2.QueryRequest.SerializeToString,
                response_deserializer=milvus__pb2.QueryResults.FromString,
                _registered_method=True)
        self.CalcDistance = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CalcDistance',
                request_serializer=milvus__pb2.CalcDistanceRequest.SerializeToString,
                response_deserializer=milvus__pb2.CalcDistanceResults.FromString,
                _registered_method=True)
        self.FlushAll = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/FlushAll',
                request_serializer=milvus__pb2.FlushAllRequest.SerializeToString,
                response_deserializer=milvus__pb2.FlushAllResponse.FromString,
                _registered_method=True)
        self.AddCollectionField = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/AddCollectionField',
                request_serializer=milvus__pb2.AddCollectionFieldRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.GetFlushState = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetFlushState',
                request_serializer=milvus__pb2.GetFlushStateRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetFlushStateResponse.FromString,
                _registered_method=True)
        self.GetFlushAllState = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetFlushAllState',
                request_serializer=milvus__pb2.GetFlushAllStateRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetFlushAllStateResponse.FromString,
                _registered_method=True)
        self.GetPersistentSegmentInfo = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetPersistentSegmentInfo',
                request_serializer=milvus__pb2.GetPersistentSegmentInfoRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetPersistentSegmentInfoResponse.FromString,
                _registered_method=True)
        self.GetQuerySegmentInfo = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetQuerySegmentInfo',
                request_serializer=milvus__pb2.GetQuerySegmentInfoRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetQuerySegmentInfoResponse.FromString,
                _registered_method=True)
        self.GetReplicas = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetReplicas',
                request_serializer=milvus__pb2.GetReplicasRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetReplicasResponse.FromString,
                _registered_method=True)
        self.Dummy = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/Dummy',
                request_serializer=milvus__pb2.DummyRequest.SerializeToString,
                response_deserializer=milvus__pb2.DummyResponse.FromString,
                _registered_method=True)
        self.RegisterLink = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/RegisterLink',
                request_serializer=milvus__pb2.RegisterLinkRequest.SerializeToString,
                response_deserializer=milvus__pb2.RegisterLinkResponse.FromString,
                _registered_method=True)
        self.GetMetrics = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetMetrics',
                request_serializer=milvus__pb2.GetMetricsRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetMetricsResponse.FromString,
                _registered_method=True)
        self.GetComponentStates = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetComponentStates',
                request_serializer=milvus__pb2.GetComponentStatesRequest.SerializeToString,
                response_deserializer=milvus__pb2.ComponentStates.FromString,
                _registered_method=True)
        self.LoadBalance = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/LoadBalance',
                request_serializer=milvus__pb2.LoadBalanceRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.GetCompactionState = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetCompactionState',
                request_serializer=milvus__pb2.GetCompactionStateRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetCompactionStateResponse.FromString,
                _registered_method=True)
        self.ManualCompaction = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ManualCompaction',
                request_serializer=milvus__pb2.ManualCompactionRequest.SerializeToString,
                response_deserializer=milvus__pb2.ManualCompactionResponse.FromString,
                _registered_method=True)
        self.GetCompactionStateWithPlans = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetCompactionStateWithPlans',
                request_serializer=milvus__pb2.GetCompactionPlansRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetCompactionPlansResponse.FromString,
                _registered_method=True)
        self.Import = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/Import',
                request_serializer=milvus__pb2.ImportRequest.SerializeToString,
                response_deserializer=milvus__pb2.ImportResponse.FromString,
                _registered_method=True)
        self.GetImportState = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetImportState',
                request_serializer=milvus__pb2.GetImportStateRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetImportStateResponse.FromString,
                _registered_method=True)
        self.ListImportTasks = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ListImportTasks',
                request_serializer=milvus__pb2.ListImportTasksRequest.SerializeToString,
                response_deserializer=milvus__pb2.ListImportTasksResponse.FromString,
                _registered_method=True)
        self.CreateCredential = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreateCredential',
                request_serializer=milvus__pb2.CreateCredentialRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.UpdateCredential = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/UpdateCredential',
                request_serializer=milvus__pb2.UpdateCredentialRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DeleteCredential = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DeleteCredential',
                request_serializer=milvus__pb2.DeleteCredentialRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.ListCredUsers = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ListCredUsers',
                request_serializer=milvus__pb2.ListCredUsersRequest.SerializeToString,
                response_deserializer=milvus__pb2.ListCredUsersResponse.FromString,
                _registered_method=True)
        self.CreateRole = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreateRole',
                request_serializer=milvus__pb2.CreateRoleRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DropRole = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DropRole',
                request_serializer=milvus__pb2.DropRoleRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.OperateUserRole = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/OperateUserRole',
                request_serializer=milvus__pb2.OperateUserRoleRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.SelectRole = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/SelectRole',
                request_serializer=milvus__pb2.SelectRoleRequest.SerializeToString,
                response_deserializer=milvus__pb2.SelectRoleResponse.FromString,
                _registered_method=True)
        self.SelectUser = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/SelectUser',
                request_serializer=milvus__pb2.SelectUserRequest.SerializeToString,
                response_deserializer=milvus__pb2.SelectUserResponse.FromString,
                _registered_method=True)
        self.OperatePrivilege = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/OperatePrivilege',
                request_serializer=milvus__pb2.OperatePrivilegeRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.OperatePrivilegeV2 = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/OperatePrivilegeV2',
                request_serializer=milvus__pb2.OperatePrivilegeV2Request.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.SelectGrant = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/SelectGrant',
                request_serializer=milvus__pb2.SelectGrantRequest.SerializeToString,
                response_deserializer=milvus__pb2.SelectGrantResponse.FromString,
                _registered_method=True)
        self.GetVersion = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetVersion',
                request_serializer=milvus__pb2.GetVersionRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetVersionResponse.FromString,
                _registered_method=True)
        self.CheckHealth = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CheckHealth',
                request_serializer=milvus__pb2.CheckHealthRequest.SerializeToString,
                response_deserializer=milvus__pb2.CheckHealthResponse.FromString,
                _registered_method=True)
        self.CreateResourceGroup = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreateResourceGroup',
                request_serializer=milvus__pb2.CreateResourceGroupRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DropResourceGroup = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DropResourceGroup',
                request_serializer=milvus__pb2.DropResourceGroupRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.UpdateResourceGroups = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/UpdateResourceGroups',
                request_serializer=milvus__pb2.UpdateResourceGroupsRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.TransferNode = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/TransferNode',
                request_serializer=milvus__pb2.TransferNodeRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.TransferReplica = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/TransferReplica',
                request_serializer=milvus__pb2.TransferReplicaRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.ListResourceGroups = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ListResourceGroups',
                request_serializer=milvus__pb2.ListResourceGroupsRequest.SerializeToString,
                response_deserializer=milvus__pb2.ListResourceGroupsResponse.FromString,
                _registered_method=True)
        self.DescribeResourceGroup = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DescribeResourceGroup',
                request_serializer=milvus__pb2.DescribeResourceGroupRequest.SerializeToString,
                response_deserializer=milvus__pb2.DescribeResourceGroupResponse.FromString,
                _registered_method=True)
        self.RenameCollection = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/RenameCollection',
                request_serializer=milvus__pb2.RenameCollectionRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.ListIndexedSegment = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ListIndexedSegment',
                request_serializer=feder__pb2.ListIndexedSegmentRequest.SerializeToString,
                response_deserializer=feder__pb2.ListIndexedSegmentResponse.FromString,
                _registered_method=True)
        self.DescribeSegmentIndexData = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DescribeSegmentIndexData',
                request_serializer=feder__pb2.DescribeSegmentIndexDataRequest.SerializeToString,
                response_deserializer=feder__pb2.DescribeSegmentIndexDataResponse.FromString,
                _registered_method=True)
        self.Connect = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/Connect',
                request_serializer=milvus__pb2.ConnectRequest.SerializeToString,
                response_deserializer=milvus__pb2.ConnectResponse.FromString,
                _registered_method=True)
        self.AllocTimestamp = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/AllocTimestamp',
                request_serializer=milvus__pb2.AllocTimestampRequest.SerializeToString,
                response_deserializer=milvus__pb2.AllocTimestampResponse.FromString,
                _registered_method=True)
        self.CreateDatabase = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreateDatabase',
                request_serializer=milvus__pb2.CreateDatabaseRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DropDatabase = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DropDatabase',
                request_serializer=milvus__pb2.DropDatabaseRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.ListDatabases = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ListDatabases',
                request_serializer=milvus__pb2.ListDatabasesRequest.SerializeToString,
                response_deserializer=milvus__pb2.ListDatabasesResponse.FromString,
                _registered_method=True)
        self.AlterDatabase = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/AlterDatabase',
                request_serializer=milvus__pb2.AlterDatabaseRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DescribeDatabase = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DescribeDatabase',
                request_serializer=milvus__pb2.DescribeDatabaseRequest.SerializeToString,
                response_deserializer=milvus__pb2.DescribeDatabaseResponse.FromString,
                _registered_method=True)
        self.ReplicateMessage = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ReplicateMessage',
                request_serializer=milvus__pb2.ReplicateMessageRequest.SerializeToString,
                response_deserializer=milvus__pb2.ReplicateMessageResponse.FromString,
                _registered_method=True)
        self.BackupRBAC = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/BackupRBAC',
                request_serializer=milvus__pb2.BackupRBACMetaRequest.SerializeToString,
                response_deserializer=milvus__pb2.BackupRBACMetaResponse.FromString,
                _registered_method=True)
        self.RestoreRBAC = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/RestoreRBAC',
                request_serializer=milvus__pb2.RestoreRBACMetaRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.CreatePrivilegeGroup = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreatePrivilegeGroup',
                request_serializer=milvus__pb2.CreatePrivilegeGroupRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DropPrivilegeGroup = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DropPrivilegeGroup',
                request_serializer=milvus__pb2.DropPrivilegeGroupRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.ListPrivilegeGroups = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ListPrivilegeGroups',
                request_serializer=milvus__pb2.ListPrivilegeGroupsRequest.SerializeToString,
                response_deserializer=milvus__pb2.ListPrivilegeGroupsResponse.FromString,
                _registered_method=True)
        self.OperatePrivilegeGroup = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/OperatePrivilegeGroup',
                request_serializer=milvus__pb2.OperatePrivilegeGroupRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.RunAnalyzer = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/RunAnalyzer',
                request_serializer=milvus__pb2.RunAnalyzerRequest.SerializeToString,
                response_deserializer=milvus__pb2.RunAnalyzerResponse.FromString,
                _registered_method=True)
        self.AddUserTags = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/AddUserTags',
                request_serializer=milvus__pb2.AddUserTagsRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DeleteUserTags = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DeleteUserTags',
                request_serializer=milvus__pb2.DeleteUserTagsRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.GetUserTags = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/GetUserTags',
                request_serializer=milvus__pb2.GetUserTagsRequest.SerializeToString,
                response_deserializer=milvus__pb2.GetUserTagsResponse.FromString,
                _registered_method=True)
        self.ListUsersWithTag = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ListUsersWithTag',
                request_serializer=milvus__pb2.ListUsersWithTagRequest.SerializeToString,
                response_deserializer=milvus__pb2.ListUsersWithTagResponse.FromString,
                _registered_method=True)
        self.CreateRowPolicy = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/CreateRowPolicy',
                request_serializer=milvus__pb2.CreateRowPolicyRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.DropRowPolicy = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/DropRowPolicy',
                request_serializer=milvus__pb2.DropRowPolicyRequest.SerializeToString,
                response_deserializer=common__pb2.Status.FromString,
                _registered_method=True)
        self.ListRowPolicies = channel.unary_unary(
                '/milvus.proto.milvus.MilvusService/ListRowPolicies',
                request_serializer=milvus__pb2.ListRowPoliciesRequest.SerializeToString,
                response_deserializer=milvus__pb2.ListRowPoliciesResponse.FromString,
                _registered_method=True)


class MilvusServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DropCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HasCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReleaseCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCollectionStatistics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ShowCollections(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AlterCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AlterCollectionField(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePartition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DropPartition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HasPartition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadPartitions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReleasePartitions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPartitionStatistics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ShowPartitions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLoadingProgress(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLoadState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateAlias(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DropAlias(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AlterAlias(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeAlias(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAliases(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateIndex(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AlterIndex(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeIndex(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIndexStatistics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIndexState(self, request, context):
        """Deprecated: use DescribeIndex instead
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIndexBuildProgress(self, request, context):
        """Deprecated: use DescribeIndex instead
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DropIndex(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Insert(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Delete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Upsert(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Search(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HybridSearch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Flush(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Query(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CalcDistance(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FlushAll(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddCollectionField(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetFlushState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetFlushAllState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPersistentSegmentInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetQuerySegmentInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetReplicas(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Dummy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RegisterLink(self, request, context):
        """TODO: remove
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMetrics(self, request, context):
        """https://wiki.lfaidata.foundation/display/MIL/MEP+8+--+Add+metrics+for+proxy
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetComponentStates(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadBalance(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCompactionState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ManualCompaction(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCompactionStateWithPlans(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Import(self, request, context):
        """https://wiki.lfaidata.foundation/display/MIL/MEP+24+--+Support+bulk+load
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetImportState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListImportTasks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateCredential(self, request, context):
        """https://wiki.lfaidata.foundation/display/MIL/MEP+27+--+Support+Basic+Authentication
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateCredential(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteCredential(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListCredUsers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateRole(self, request, context):
        """https://wiki.lfaidata.foundation/display/MIL/MEP+29+--+Support+Role-Based+Access+Control
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DropRole(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OperateUserRole(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SelectRole(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SelectUser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OperatePrivilege(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OperatePrivilegeV2(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SelectGrant(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetVersion(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckHealth(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateResourceGroup(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DropResourceGroup(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateResourceGroups(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TransferNode(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TransferReplica(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListResourceGroups(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeResourceGroup(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RenameCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListIndexedSegment(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeSegmentIndexData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Connect(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AllocTimestamp(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDatabase(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DropDatabase(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListDatabases(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AlterDatabase(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeDatabase(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReplicateMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BackupRBAC(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RestoreRBAC(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePrivilegeGroup(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DropPrivilegeGroup(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListPrivilegeGroups(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OperatePrivilegeGroup(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RunAnalyzer(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddUserTags(self, request, context):
        """Row Level Security (RLS) APIs
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteUserTags(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetUserTags(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListUsersWithTag(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateRowPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DropRowPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListRowPolicies(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_MilvusServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateCollection,
                    request_deserializer=milvus__pb2.CreateCollectionRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DropCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.DropCollection,
                    request_deserializer=milvus__pb2.DropCollectionRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'HasCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.HasCollection,
                    request_deserializer=milvus__pb2.HasCollectionRequest.FromString,
                    response_serializer=milvus__pb2.BoolResponse.SerializeToString,
            ),
            'LoadCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadCollection,
                    request_deserializer=milvus__pb2.LoadCollectionRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'ReleaseCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.ReleaseCollection,
                    request_deserializer=milvus__pb2.ReleaseCollectionRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DescribeCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeCollection,
                    request_deserializer=milvus__pb2.DescribeCollectionRequest.FromString,
                    response_serializer=milvus__pb2.DescribeCollectionResponse.SerializeToString,
            ),
            'GetCollectionStatistics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCollectionStatistics,
                    request_deserializer=milvus__pb2.GetCollectionStatisticsRequest.FromString,
                    response_serializer=milvus__pb2.GetCollectionStatisticsResponse.SerializeToString,
            ),
            'ShowCollections': grpc.unary_unary_rpc_method_handler(
                    servicer.ShowCollections,
                    request_deserializer=milvus__pb2.ShowCollectionsRequest.FromString,
                    response_serializer=milvus__pb2.ShowCollectionsResponse.SerializeToString,
            ),
            'AlterCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.AlterCollection,
                    request_deserializer=milvus__pb2.AlterCollectionRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'AlterCollectionField': grpc.unary_unary_rpc_method_handler(
                    servicer.AlterCollectionField,
                    request_deserializer=milvus__pb2.AlterCollectionFieldRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'CreatePartition': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePartition,
                    request_deserializer=milvus__pb2.CreatePartitionRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DropPartition': grpc.unary_unary_rpc_method_handler(
                    servicer.DropPartition,
                    request_deserializer=milvus__pb2.DropPartitionRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'HasPartition': grpc.unary_unary_rpc_method_handler(
                    servicer.HasPartition,
                    request_deserializer=milvus__pb2.HasPartitionRequest.FromString,
                    response_serializer=milvus__pb2.BoolResponse.SerializeToString,
            ),
            'LoadPartitions': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadPartitions,
                    request_deserializer=milvus__pb2.LoadPartitionsRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'ReleasePartitions': grpc.unary_unary_rpc_method_handler(
                    servicer.ReleasePartitions,
                    request_deserializer=milvus__pb2.ReleasePartitionsRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'GetPartitionStatistics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPartitionStatistics,
                    request_deserializer=milvus__pb2.GetPartitionStatisticsRequest.FromString,
                    response_serializer=milvus__pb2.GetPartitionStatisticsResponse.SerializeToString,
            ),
            'ShowPartitions': grpc.unary_unary_rpc_method_handler(
                    servicer.ShowPartitions,
                    request_deserializer=milvus__pb2.ShowPartitionsRequest.FromString,
                    response_serializer=milvus__pb2.ShowPartitionsResponse.SerializeToString,
            ),
            'GetLoadingProgress': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLoadingProgress,
                    request_deserializer=milvus__pb2.GetLoadingProgressRequest.FromString,
                    response_serializer=milvus__pb2.GetLoadingProgressResponse.SerializeToString,
            ),
            'GetLoadState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLoadState,
                    request_deserializer=milvus__pb2.GetLoadStateRequest.FromString,
                    response_serializer=milvus__pb2.GetLoadStateResponse.SerializeToString,
            ),
            'CreateAlias': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateAlias,
                    request_deserializer=milvus__pb2.CreateAliasRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DropAlias': grpc.unary_unary_rpc_method_handler(
                    servicer.DropAlias,
                    request_deserializer=milvus__pb2.DropAliasRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'AlterAlias': grpc.unary_unary_rpc_method_handler(
                    servicer.AlterAlias,
                    request_deserializer=milvus__pb2.AlterAliasRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DescribeAlias': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeAlias,
                    request_deserializer=milvus__pb2.DescribeAliasRequest.FromString,
                    response_serializer=milvus__pb2.DescribeAliasResponse.SerializeToString,
            ),
            'ListAliases': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAliases,
                    request_deserializer=milvus__pb2.ListAliasesRequest.FromString,
                    response_serializer=milvus__pb2.ListAliasesResponse.SerializeToString,
            ),
            'CreateIndex': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateIndex,
                    request_deserializer=milvus__pb2.CreateIndexRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'AlterIndex': grpc.unary_unary_rpc_method_handler(
                    servicer.AlterIndex,
                    request_deserializer=milvus__pb2.AlterIndexRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DescribeIndex': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeIndex,
                    request_deserializer=milvus__pb2.DescribeIndexRequest.FromString,
                    response_serializer=milvus__pb2.DescribeIndexResponse.SerializeToString,
            ),
            'GetIndexStatistics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIndexStatistics,
                    request_deserializer=milvus__pb2.GetIndexStatisticsRequest.FromString,
                    response_serializer=milvus__pb2.GetIndexStatisticsResponse.SerializeToString,
            ),
            'GetIndexState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIndexState,
                    request_deserializer=milvus__pb2.GetIndexStateRequest.FromString,
                    response_serializer=milvus__pb2.GetIndexStateResponse.SerializeToString,
            ),
            'GetIndexBuildProgress': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIndexBuildProgress,
                    request_deserializer=milvus__pb2.GetIndexBuildProgressRequest.FromString,
                    response_serializer=milvus__pb2.GetIndexBuildProgressResponse.SerializeToString,
            ),
            'DropIndex': grpc.unary_unary_rpc_method_handler(
                    servicer.DropIndex,
                    request_deserializer=milvus__pb2.DropIndexRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'Insert': grpc.unary_unary_rpc_method_handler(
                    servicer.Insert,
                    request_deserializer=milvus__pb2.InsertRequest.FromString,
                    response_serializer=milvus__pb2.MutationResult.SerializeToString,
            ),
            'Delete': grpc.unary_unary_rpc_method_handler(
                    servicer.Delete,
                    request_deserializer=milvus__pb2.DeleteRequest.FromString,
                    response_serializer=milvus__pb2.MutationResult.SerializeToString,
            ),
            'Upsert': grpc.unary_unary_rpc_method_handler(
                    servicer.Upsert,
                    request_deserializer=milvus__pb2.UpsertRequest.FromString,
                    response_serializer=milvus__pb2.MutationResult.SerializeToString,
            ),
            'Search': grpc.unary_unary_rpc_method_handler(
                    servicer.Search,
                    request_deserializer=milvus__pb2.SearchRequest.FromString,
                    response_serializer=milvus__pb2.SearchResults.SerializeToString,
            ),
            'HybridSearch': grpc.unary_unary_rpc_method_handler(
                    servicer.HybridSearch,
                    request_deserializer=milvus__pb2.HybridSearchRequest.FromString,
                    response_serializer=milvus__pb2.SearchResults.SerializeToString,
            ),
            'Flush': grpc.unary_unary_rpc_method_handler(
                    servicer.Flush,
                    request_deserializer=milvus__pb2.FlushRequest.FromString,
                    response_serializer=milvus__pb2.FlushResponse.SerializeToString,
            ),
            'Query': grpc.unary_unary_rpc_method_handler(
                    servicer.Query,
                    request_deserializer=milvus__pb2.QueryRequest.FromString,
                    response_serializer=milvus__pb2.QueryResults.SerializeToString,
            ),
            'CalcDistance': grpc.unary_unary_rpc_method_handler(
                    servicer.CalcDistance,
                    request_deserializer=milvus__pb2.CalcDistanceRequest.FromString,
                    response_serializer=milvus__pb2.CalcDistanceResults.SerializeToString,
            ),
            'FlushAll': grpc.unary_unary_rpc_method_handler(
                    servicer.FlushAll,
                    request_deserializer=milvus__pb2.FlushAllRequest.FromString,
                    response_serializer=milvus__pb2.FlushAllResponse.SerializeToString,
            ),
            'AddCollectionField': grpc.unary_unary_rpc_method_handler(
                    servicer.AddCollectionField,
                    request_deserializer=milvus__pb2.AddCollectionFieldRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'GetFlushState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetFlushState,
                    request_deserializer=milvus__pb2.GetFlushStateRequest.FromString,
                    response_serializer=milvus__pb2.GetFlushStateResponse.SerializeToString,
            ),
            'GetFlushAllState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetFlushAllState,
                    request_deserializer=milvus__pb2.GetFlushAllStateRequest.FromString,
                    response_serializer=milvus__pb2.GetFlushAllStateResponse.SerializeToString,
            ),
            'GetPersistentSegmentInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPersistentSegmentInfo,
                    request_deserializer=milvus__pb2.GetPersistentSegmentInfoRequest.FromString,
                    response_serializer=milvus__pb2.GetPersistentSegmentInfoResponse.SerializeToString,
            ),
            'GetQuerySegmentInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetQuerySegmentInfo,
                    request_deserializer=milvus__pb2.GetQuerySegmentInfoRequest.FromString,
                    response_serializer=milvus__pb2.GetQuerySegmentInfoResponse.SerializeToString,
            ),
            'GetReplicas': grpc.unary_unary_rpc_method_handler(
                    servicer.GetReplicas,
                    request_deserializer=milvus__pb2.GetReplicasRequest.FromString,
                    response_serializer=milvus__pb2.GetReplicasResponse.SerializeToString,
            ),
            'Dummy': grpc.unary_unary_rpc_method_handler(
                    servicer.Dummy,
                    request_deserializer=milvus__pb2.DummyRequest.FromString,
                    response_serializer=milvus__pb2.DummyResponse.SerializeToString,
            ),
            'RegisterLink': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterLink,
                    request_deserializer=milvus__pb2.RegisterLinkRequest.FromString,
                    response_serializer=milvus__pb2.RegisterLinkResponse.SerializeToString,
            ),
            'GetMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMetrics,
                    request_deserializer=milvus__pb2.GetMetricsRequest.FromString,
                    response_serializer=milvus__pb2.GetMetricsResponse.SerializeToString,
            ),
            'GetComponentStates': grpc.unary_unary_rpc_method_handler(
                    servicer.GetComponentStates,
                    request_deserializer=milvus__pb2.GetComponentStatesRequest.FromString,
                    response_serializer=milvus__pb2.ComponentStates.SerializeToString,
            ),
            'LoadBalance': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadBalance,
                    request_deserializer=milvus__pb2.LoadBalanceRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'GetCompactionState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCompactionState,
                    request_deserializer=milvus__pb2.GetCompactionStateRequest.FromString,
                    response_serializer=milvus__pb2.GetCompactionStateResponse.SerializeToString,
            ),
            'ManualCompaction': grpc.unary_unary_rpc_method_handler(
                    servicer.ManualCompaction,
                    request_deserializer=milvus__pb2.ManualCompactionRequest.FromString,
                    response_serializer=milvus__pb2.ManualCompactionResponse.SerializeToString,
            ),
            'GetCompactionStateWithPlans': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCompactionStateWithPlans,
                    request_deserializer=milvus__pb2.GetCompactionPlansRequest.FromString,
                    response_serializer=milvus__pb2.GetCompactionPlansResponse.SerializeToString,
            ),
            'Import': grpc.unary_unary_rpc_method_handler(
                    servicer.Import,
                    request_deserializer=milvus__pb2.ImportRequest.FromString,
                    response_serializer=milvus__pb2.ImportResponse.SerializeToString,
            ),
            'GetImportState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetImportState,
                    request_deserializer=milvus__pb2.GetImportStateRequest.FromString,
                    response_serializer=milvus__pb2.GetImportStateResponse.SerializeToString,
            ),
            'ListImportTasks': grpc.unary_unary_rpc_method_handler(
                    servicer.ListImportTasks,
                    request_deserializer=milvus__pb2.ListImportTasksRequest.FromString,
                    response_serializer=milvus__pb2.ListImportTasksResponse.SerializeToString,
            ),
            'CreateCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateCredential,
                    request_deserializer=milvus__pb2.CreateCredentialRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'UpdateCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateCredential,
                    request_deserializer=milvus__pb2.UpdateCredentialRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DeleteCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteCredential,
                    request_deserializer=milvus__pb2.DeleteCredentialRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'ListCredUsers': grpc.unary_unary_rpc_method_handler(
                    servicer.ListCredUsers,
                    request_deserializer=milvus__pb2.ListCredUsersRequest.FromString,
                    response_serializer=milvus__pb2.ListCredUsersResponse.SerializeToString,
            ),
            'CreateRole': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateRole,
                    request_deserializer=milvus__pb2.CreateRoleRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DropRole': grpc.unary_unary_rpc_method_handler(
                    servicer.DropRole,
                    request_deserializer=milvus__pb2.DropRoleRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'OperateUserRole': grpc.unary_unary_rpc_method_handler(
                    servicer.OperateUserRole,
                    request_deserializer=milvus__pb2.OperateUserRoleRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'SelectRole': grpc.unary_unary_rpc_method_handler(
                    servicer.SelectRole,
                    request_deserializer=milvus__pb2.SelectRoleRequest.FromString,
                    response_serializer=milvus__pb2.SelectRoleResponse.SerializeToString,
            ),
            'SelectUser': grpc.unary_unary_rpc_method_handler(
                    servicer.SelectUser,
                    request_deserializer=milvus__pb2.SelectUserRequest.FromString,
                    response_serializer=milvus__pb2.SelectUserResponse.SerializeToString,
            ),
            'OperatePrivilege': grpc.unary_unary_rpc_method_handler(
                    servicer.OperatePrivilege,
                    request_deserializer=milvus__pb2.OperatePrivilegeRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'OperatePrivilegeV2': grpc.unary_unary_rpc_method_handler(
                    servicer.OperatePrivilegeV2,
                    request_deserializer=milvus__pb2.OperatePrivilegeV2Request.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'SelectGrant': grpc.unary_unary_rpc_method_handler(
                    servicer.SelectGrant,
                    request_deserializer=milvus__pb2.SelectGrantRequest.FromString,
                    response_serializer=milvus__pb2.SelectGrantResponse.SerializeToString,
            ),
            'GetVersion': grpc.unary_unary_rpc_method_handler(
                    servicer.GetVersion,
                    request_deserializer=milvus__pb2.GetVersionRequest.FromString,
                    response_serializer=milvus__pb2.GetVersionResponse.SerializeToString,
            ),
            'CheckHealth': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckHealth,
                    request_deserializer=milvus__pb2.CheckHealthRequest.FromString,
                    response_serializer=milvus__pb2.CheckHealthResponse.SerializeToString,
            ),
            'CreateResourceGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateResourceGroup,
                    request_deserializer=milvus__pb2.CreateResourceGroupRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DropResourceGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.DropResourceGroup,
                    request_deserializer=milvus__pb2.DropResourceGroupRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'UpdateResourceGroups': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateResourceGroups,
                    request_deserializer=milvus__pb2.UpdateResourceGroupsRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'TransferNode': grpc.unary_unary_rpc_method_handler(
                    servicer.TransferNode,
                    request_deserializer=milvus__pb2.TransferNodeRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'TransferReplica': grpc.unary_unary_rpc_method_handler(
                    servicer.TransferReplica,
                    request_deserializer=milvus__pb2.TransferReplicaRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'ListResourceGroups': grpc.unary_unary_rpc_method_handler(
                    servicer.ListResourceGroups,
                    request_deserializer=milvus__pb2.ListResourceGroupsRequest.FromString,
                    response_serializer=milvus__pb2.ListResourceGroupsResponse.SerializeToString,
            ),
            'DescribeResourceGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeResourceGroup,
                    request_deserializer=milvus__pb2.DescribeResourceGroupRequest.FromString,
                    response_serializer=milvus__pb2.DescribeResourceGroupResponse.SerializeToString,
            ),
            'RenameCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.RenameCollection,
                    request_deserializer=milvus__pb2.RenameCollectionRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'ListIndexedSegment': grpc.unary_unary_rpc_method_handler(
                    servicer.ListIndexedSegment,
                    request_deserializer=feder__pb2.ListIndexedSegmentRequest.FromString,
                    response_serializer=feder__pb2.ListIndexedSegmentResponse.SerializeToString,
            ),
            'DescribeSegmentIndexData': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeSegmentIndexData,
                    request_deserializer=feder__pb2.DescribeSegmentIndexDataRequest.FromString,
                    response_serializer=feder__pb2.DescribeSegmentIndexDataResponse.SerializeToString,
            ),
            'Connect': grpc.unary_unary_rpc_method_handler(
                    servicer.Connect,
                    request_deserializer=milvus__pb2.ConnectRequest.FromString,
                    response_serializer=milvus__pb2.ConnectResponse.SerializeToString,
            ),
            'AllocTimestamp': grpc.unary_unary_rpc_method_handler(
                    servicer.AllocTimestamp,
                    request_deserializer=milvus__pb2.AllocTimestampRequest.FromString,
                    response_serializer=milvus__pb2.AllocTimestampResponse.SerializeToString,
            ),
            'CreateDatabase': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDatabase,
                    request_deserializer=milvus__pb2.CreateDatabaseRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DropDatabase': grpc.unary_unary_rpc_method_handler(
                    servicer.DropDatabase,
                    request_deserializer=milvus__pb2.DropDatabaseRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'ListDatabases': grpc.unary_unary_rpc_method_handler(
                    servicer.ListDatabases,
                    request_deserializer=milvus__pb2.ListDatabasesRequest.FromString,
                    response_serializer=milvus__pb2.ListDatabasesResponse.SerializeToString,
            ),
            'AlterDatabase': grpc.unary_unary_rpc_method_handler(
                    servicer.AlterDatabase,
                    request_deserializer=milvus__pb2.AlterDatabaseRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DescribeDatabase': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeDatabase,
                    request_deserializer=milvus__pb2.DescribeDatabaseRequest.FromString,
                    response_serializer=milvus__pb2.DescribeDatabaseResponse.SerializeToString,
            ),
            'ReplicateMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.ReplicateMessage,
                    request_deserializer=milvus__pb2.ReplicateMessageRequest.FromString,
                    response_serializer=milvus__pb2.ReplicateMessageResponse.SerializeToString,
            ),
            'BackupRBAC': grpc.unary_unary_rpc_method_handler(
                    servicer.BackupRBAC,
                    request_deserializer=milvus__pb2.BackupRBACMetaRequest.FromString,
                    response_serializer=milvus__pb2.BackupRBACMetaResponse.SerializeToString,
            ),
            'RestoreRBAC': grpc.unary_unary_rpc_method_handler(
                    servicer.RestoreRBAC,
                    request_deserializer=milvus__pb2.RestoreRBACMetaRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'CreatePrivilegeGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePrivilegeGroup,
                    request_deserializer=milvus__pb2.CreatePrivilegeGroupRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DropPrivilegeGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.DropPrivilegeGroup,
                    request_deserializer=milvus__pb2.DropPrivilegeGroupRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'ListPrivilegeGroups': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPrivilegeGroups,
                    request_deserializer=milvus__pb2.ListPrivilegeGroupsRequest.FromString,
                    response_serializer=milvus__pb2.ListPrivilegeGroupsResponse.SerializeToString,
            ),
            'OperatePrivilegeGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.OperatePrivilegeGroup,
                    request_deserializer=milvus__pb2.OperatePrivilegeGroupRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'RunAnalyzer': grpc.unary_unary_rpc_method_handler(
                    servicer.RunAnalyzer,
                    request_deserializer=milvus__pb2.RunAnalyzerRequest.FromString,
                    response_serializer=milvus__pb2.RunAnalyzerResponse.SerializeToString,
            ),
            'AddUserTags': grpc.unary_unary_rpc_method_handler(
                    servicer.AddUserTags,
                    request_deserializer=milvus__pb2.AddUserTagsRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DeleteUserTags': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteUserTags,
                    request_deserializer=milvus__pb2.DeleteUserTagsRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'GetUserTags': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUserTags,
                    request_deserializer=milvus__pb2.GetUserTagsRequest.FromString,
                    response_serializer=milvus__pb2.GetUserTagsResponse.SerializeToString,
            ),
            'ListUsersWithTag': grpc.unary_unary_rpc_method_handler(
                    servicer.ListUsersWithTag,
                    request_deserializer=milvus__pb2.ListUsersWithTagRequest.FromString,
                    response_serializer=milvus__pb2.ListUsersWithTagResponse.SerializeToString,
            ),
            'CreateRowPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateRowPolicy,
                    request_deserializer=milvus__pb2.CreateRowPolicyRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'DropRowPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.DropRowPolicy,
                    request_deserializer=milvus__pb2.DropRowPolicyRequest.FromString,
                    response_serializer=common__pb2.Status.SerializeToString,
            ),
            'ListRowPolicies': grpc.unary_unary_rpc_method_handler(
                    servicer.ListRowPolicies,
                    request_deserializer=milvus__pb2.ListRowPoliciesRequest.FromString,
                    response_serializer=milvus__pb2.ListRowPoliciesResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'milvus.proto.milvus.MilvusService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('milvus.proto.milvus.MilvusService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class MilvusService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreateCollection',
            milvus__pb2.CreateCollectionRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DropCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DropCollection',
            milvus__pb2.DropCollectionRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HasCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/HasCollection',
            milvus__pb2.HasCollectionRequest.SerializeToString,
            milvus__pb2.BoolResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LoadCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/LoadCollection',
            milvus__pb2.LoadCollectionRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReleaseCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ReleaseCollection',
            milvus__pb2.ReleaseCollectionRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DescribeCollection',
            milvus__pb2.DescribeCollectionRequest.SerializeToString,
            milvus__pb2.DescribeCollectionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCollectionStatistics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetCollectionStatistics',
            milvus__pb2.GetCollectionStatisticsRequest.SerializeToString,
            milvus__pb2.GetCollectionStatisticsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ShowCollections(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ShowCollections',
            milvus__pb2.ShowCollectionsRequest.SerializeToString,
            milvus__pb2.ShowCollectionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AlterCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/AlterCollection',
            milvus__pb2.AlterCollectionRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AlterCollectionField(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/AlterCollectionField',
            milvus__pb2.AlterCollectionFieldRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePartition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreatePartition',
            milvus__pb2.CreatePartitionRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DropPartition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DropPartition',
            milvus__pb2.DropPartitionRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HasPartition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/HasPartition',
            milvus__pb2.HasPartitionRequest.SerializeToString,
            milvus__pb2.BoolResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LoadPartitions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/LoadPartitions',
            milvus__pb2.LoadPartitionsRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReleasePartitions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ReleasePartitions',
            milvus__pb2.ReleasePartitionsRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPartitionStatistics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetPartitionStatistics',
            milvus__pb2.GetPartitionStatisticsRequest.SerializeToString,
            milvus__pb2.GetPartitionStatisticsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ShowPartitions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ShowPartitions',
            milvus__pb2.ShowPartitionsRequest.SerializeToString,
            milvus__pb2.ShowPartitionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLoadingProgress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetLoadingProgress',
            milvus__pb2.GetLoadingProgressRequest.SerializeToString,
            milvus__pb2.GetLoadingProgressResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLoadState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetLoadState',
            milvus__pb2.GetLoadStateRequest.SerializeToString,
            milvus__pb2.GetLoadStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateAlias(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreateAlias',
            milvus__pb2.CreateAliasRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DropAlias(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DropAlias',
            milvus__pb2.DropAliasRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AlterAlias(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/AlterAlias',
            milvus__pb2.AlterAliasRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeAlias(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DescribeAlias',
            milvus__pb2.DescribeAliasRequest.SerializeToString,
            milvus__pb2.DescribeAliasResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListAliases(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ListAliases',
            milvus__pb2.ListAliasesRequest.SerializeToString,
            milvus__pb2.ListAliasesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateIndex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreateIndex',
            milvus__pb2.CreateIndexRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AlterIndex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/AlterIndex',
            milvus__pb2.AlterIndexRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeIndex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DescribeIndex',
            milvus__pb2.DescribeIndexRequest.SerializeToString,
            milvus__pb2.DescribeIndexResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIndexStatistics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetIndexStatistics',
            milvus__pb2.GetIndexStatisticsRequest.SerializeToString,
            milvus__pb2.GetIndexStatisticsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIndexState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetIndexState',
            milvus__pb2.GetIndexStateRequest.SerializeToString,
            milvus__pb2.GetIndexStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIndexBuildProgress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetIndexBuildProgress',
            milvus__pb2.GetIndexBuildProgressRequest.SerializeToString,
            milvus__pb2.GetIndexBuildProgressResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DropIndex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DropIndex',
            milvus__pb2.DropIndexRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Insert(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/Insert',
            milvus__pb2.InsertRequest.SerializeToString,
            milvus__pb2.MutationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Delete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/Delete',
            milvus__pb2.DeleteRequest.SerializeToString,
            milvus__pb2.MutationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Upsert(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/Upsert',
            milvus__pb2.UpsertRequest.SerializeToString,
            milvus__pb2.MutationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Search(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/Search',
            milvus__pb2.SearchRequest.SerializeToString,
            milvus__pb2.SearchResults.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HybridSearch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/HybridSearch',
            milvus__pb2.HybridSearchRequest.SerializeToString,
            milvus__pb2.SearchResults.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Flush(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/Flush',
            milvus__pb2.FlushRequest.SerializeToString,
            milvus__pb2.FlushResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Query(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/Query',
            milvus__pb2.QueryRequest.SerializeToString,
            milvus__pb2.QueryResults.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CalcDistance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CalcDistance',
            milvus__pb2.CalcDistanceRequest.SerializeToString,
            milvus__pb2.CalcDistanceResults.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FlushAll(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/FlushAll',
            milvus__pb2.FlushAllRequest.SerializeToString,
            milvus__pb2.FlushAllResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AddCollectionField(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/AddCollectionField',
            milvus__pb2.AddCollectionFieldRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetFlushState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetFlushState',
            milvus__pb2.GetFlushStateRequest.SerializeToString,
            milvus__pb2.GetFlushStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetFlushAllState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetFlushAllState',
            milvus__pb2.GetFlushAllStateRequest.SerializeToString,
            milvus__pb2.GetFlushAllStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPersistentSegmentInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetPersistentSegmentInfo',
            milvus__pb2.GetPersistentSegmentInfoRequest.SerializeToString,
            milvus__pb2.GetPersistentSegmentInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetQuerySegmentInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetQuerySegmentInfo',
            milvus__pb2.GetQuerySegmentInfoRequest.SerializeToString,
            milvus__pb2.GetQuerySegmentInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetReplicas(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetReplicas',
            milvus__pb2.GetReplicasRequest.SerializeToString,
            milvus__pb2.GetReplicasResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Dummy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/Dummy',
            milvus__pb2.DummyRequest.SerializeToString,
            milvus__pb2.DummyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RegisterLink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/RegisterLink',
            milvus__pb2.RegisterLinkRequest.SerializeToString,
            milvus__pb2.RegisterLinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetMetrics',
            milvus__pb2.GetMetricsRequest.SerializeToString,
            milvus__pb2.GetMetricsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetComponentStates(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetComponentStates',
            milvus__pb2.GetComponentStatesRequest.SerializeToString,
            milvus__pb2.ComponentStates.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LoadBalance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/LoadBalance',
            milvus__pb2.LoadBalanceRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCompactionState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetCompactionState',
            milvus__pb2.GetCompactionStateRequest.SerializeToString,
            milvus__pb2.GetCompactionStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ManualCompaction(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ManualCompaction',
            milvus__pb2.ManualCompactionRequest.SerializeToString,
            milvus__pb2.ManualCompactionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetCompactionStateWithPlans(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetCompactionStateWithPlans',
            milvus__pb2.GetCompactionPlansRequest.SerializeToString,
            milvus__pb2.GetCompactionPlansResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Import(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/Import',
            milvus__pb2.ImportRequest.SerializeToString,
            milvus__pb2.ImportResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetImportState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetImportState',
            milvus__pb2.GetImportStateRequest.SerializeToString,
            milvus__pb2.GetImportStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListImportTasks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ListImportTasks',
            milvus__pb2.ListImportTasksRequest.SerializeToString,
            milvus__pb2.ListImportTasksResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreateCredential',
            milvus__pb2.CreateCredentialRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/UpdateCredential',
            milvus__pb2.UpdateCredentialRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DeleteCredential',
            milvus__pb2.DeleteCredentialRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListCredUsers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ListCredUsers',
            milvus__pb2.ListCredUsersRequest.SerializeToString,
            milvus__pb2.ListCredUsersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreateRole',
            milvus__pb2.CreateRoleRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DropRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DropRole',
            milvus__pb2.DropRoleRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OperateUserRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/OperateUserRole',
            milvus__pb2.OperateUserRoleRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SelectRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/SelectRole',
            milvus__pb2.SelectRoleRequest.SerializeToString,
            milvus__pb2.SelectRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SelectUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/SelectUser',
            milvus__pb2.SelectUserRequest.SerializeToString,
            milvus__pb2.SelectUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OperatePrivilege(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/OperatePrivilege',
            milvus__pb2.OperatePrivilegeRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OperatePrivilegeV2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/OperatePrivilegeV2',
            milvus__pb2.OperatePrivilegeV2Request.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SelectGrant(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/SelectGrant',
            milvus__pb2.SelectGrantRequest.SerializeToString,
            milvus__pb2.SelectGrantResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetVersion(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetVersion',
            milvus__pb2.GetVersionRequest.SerializeToString,
            milvus__pb2.GetVersionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CheckHealth(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CheckHealth',
            milvus__pb2.CheckHealthRequest.SerializeToString,
            milvus__pb2.CheckHealthResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateResourceGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreateResourceGroup',
            milvus__pb2.CreateResourceGroupRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DropResourceGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DropResourceGroup',
            milvus__pb2.DropResourceGroupRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateResourceGroups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/UpdateResourceGroups',
            milvus__pb2.UpdateResourceGroupsRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TransferNode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/TransferNode',
            milvus__pb2.TransferNodeRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TransferReplica(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/TransferReplica',
            milvus__pb2.TransferReplicaRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListResourceGroups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ListResourceGroups',
            milvus__pb2.ListResourceGroupsRequest.SerializeToString,
            milvus__pb2.ListResourceGroupsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeResourceGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DescribeResourceGroup',
            milvus__pb2.DescribeResourceGroupRequest.SerializeToString,
            milvus__pb2.DescribeResourceGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RenameCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/RenameCollection',
            milvus__pb2.RenameCollectionRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListIndexedSegment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ListIndexedSegment',
            feder__pb2.ListIndexedSegmentRequest.SerializeToString,
            feder__pb2.ListIndexedSegmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeSegmentIndexData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DescribeSegmentIndexData',
            feder__pb2.DescribeSegmentIndexDataRequest.SerializeToString,
            feder__pb2.DescribeSegmentIndexDataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Connect(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/Connect',
            milvus__pb2.ConnectRequest.SerializeToString,
            milvus__pb2.ConnectResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AllocTimestamp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/AllocTimestamp',
            milvus__pb2.AllocTimestampRequest.SerializeToString,
            milvus__pb2.AllocTimestampResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateDatabase(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreateDatabase',
            milvus__pb2.CreateDatabaseRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DropDatabase(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DropDatabase',
            milvus__pb2.DropDatabaseRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListDatabases(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ListDatabases',
            milvus__pb2.ListDatabasesRequest.SerializeToString,
            milvus__pb2.ListDatabasesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AlterDatabase(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/AlterDatabase',
            milvus__pb2.AlterDatabaseRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeDatabase(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DescribeDatabase',
            milvus__pb2.DescribeDatabaseRequest.SerializeToString,
            milvus__pb2.DescribeDatabaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReplicateMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ReplicateMessage',
            milvus__pb2.ReplicateMessageRequest.SerializeToString,
            milvus__pb2.ReplicateMessageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BackupRBAC(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/BackupRBAC',
            milvus__pb2.BackupRBACMetaRequest.SerializeToString,
            milvus__pb2.BackupRBACMetaResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RestoreRBAC(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/RestoreRBAC',
            milvus__pb2.RestoreRBACMetaRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePrivilegeGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreatePrivilegeGroup',
            milvus__pb2.CreatePrivilegeGroupRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DropPrivilegeGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DropPrivilegeGroup',
            milvus__pb2.DropPrivilegeGroupRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListPrivilegeGroups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ListPrivilegeGroups',
            milvus__pb2.ListPrivilegeGroupsRequest.SerializeToString,
            milvus__pb2.ListPrivilegeGroupsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def OperatePrivilegeGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/OperatePrivilegeGroup',
            milvus__pb2.OperatePrivilegeGroupRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RunAnalyzer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/RunAnalyzer',
            milvus__pb2.RunAnalyzerRequest.SerializeToString,
            milvus__pb2.RunAnalyzerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AddUserTags(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/AddUserTags',
            milvus__pb2.AddUserTagsRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteUserTags(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DeleteUserTags',
            milvus__pb2.DeleteUserTagsRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetUserTags(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/GetUserTags',
            milvus__pb2.GetUserTagsRequest.SerializeToString,
            milvus__pb2.GetUserTagsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListUsersWithTag(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ListUsersWithTag',
            milvus__pb2.ListUsersWithTagRequest.SerializeToString,
            milvus__pb2.ListUsersWithTagResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateRowPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/CreateRowPolicy',
            milvus__pb2.CreateRowPolicyRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DropRowPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/DropRowPolicy',
            milvus__pb2.DropRowPolicyRequest.SerializeToString,
            common__pb2.Status.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListRowPolicies(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.MilvusService/ListRowPolicies',
            milvus__pb2.ListRowPoliciesRequest.SerializeToString,
            milvus__pb2.ListRowPoliciesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class ProxyServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.RegisterLink = channel.unary_unary(
                '/milvus.proto.milvus.ProxyService/RegisterLink',
                request_serializer=milvus__pb2.RegisterLinkRequest.SerializeToString,
                response_deserializer=milvus__pb2.RegisterLinkResponse.FromString,
                _registered_method=True)


class ProxyServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def RegisterLink(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ProxyServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'RegisterLink': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterLink,
                    request_deserializer=milvus__pb2.RegisterLinkRequest.FromString,
                    response_serializer=milvus__pb2.RegisterLinkResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'milvus.proto.milvus.ProxyService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('milvus.proto.milvus.ProxyService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ProxyService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def RegisterLink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/milvus.proto.milvus.ProxyService/RegisterLink',
            milvus__pb2.RegisterLinkRequest.SerializeToString,
            milvus__pb2.RegisterLinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
