# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: milvus.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'milvus.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import rg_pb2 as rg__pb2
from . import schema_pb2 as schema__pb2
from . import feder_pb2 as feder__pb2
from . import msg_pb2 as msg__pb2
from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0cmilvus.proto\x12\x13milvus.proto.milvus\x1a\x0c\x63ommon.proto\x1a\x08rg.proto\x1a\x0cschema.proto\x1a\x0b\x66\x65\x64\x65r.proto\x1a\tmsg.proto\x1a google/protobuf/descriptor.proto\"\x8d\x01\n\x12\x43reateAliasRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\r\n\x05\x61lias\x18\x04 \x01(\t:\x12\xca>\x0f\x08\x01\x10,\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"r\n\x10\x44ropAliasRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\r\n\x05\x61lias\x18\x03 \x01(\t:\x12\xca>\x0f\x08\x01\x10-\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x8c\x01\n\x11\x41lterAliasRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\r\n\x05\x61lias\x18\x04 \x01(\t:\x12\xca>\x0f\x08\x01\x10,\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"v\n\x14\x44\x65scribeAliasRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\r\n\x05\x61lias\x18\x03 \x01(\t:\x12\xca>\x0f\x08\x01\x10.\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"x\n\x15\x44\x65scribeAliasResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\r\n\x05\x61lias\x18\x03 \x01(\t\x12\x12\n\ncollection\x18\x04 \x01(\t\"~\n\x12ListAliasesRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t:\x12\xca>\x0f\x08\x01\x10/\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"}\n\x13ListAliasesResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x0f\n\x07\x61liases\x18\x04 \x03(\t\"\xb8\x02\n\x17\x43reateCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x0e\n\x06schema\x18\x04 \x01(\x0c\x12\x12\n\nshards_num\x18\x05 \x01(\x05\x12@\n\x11\x63onsistency_level\x18\x06 \x01(\x0e\x32%.milvus.proto.common.ConsistencyLevel\x12\x35\n\nproperties\x18\x07 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x16\n\x0enum_partitions\x18\x08 \x01(\x03:\x12\xca>\x0f\x08\x01\x10\x01\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x81\x01\n\x15\x44ropCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x02\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xe4\x01\n\x16\x41lterCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x14\n\x0c\x63ollectionID\x18\x04 \x01(\x03\x12\x35\n\nproperties\x18\x05 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x13\n\x0b\x64\x65lete_keys\x18\x06 \x03(\t:\x12\xca>\x0f\x08\x01\x10\x01\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xe7\x01\n\x1b\x41lterCollectionFieldRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\nfield_name\x18\x04 \x01(\t\x12\x35\n\nproperties\x18\x05 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x13\n\x0b\x64\x65lete_keys\x18\x06 \x03(\t:\x12\xca>\x0f\x08\x01\x10\x01\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x80\x01\n\x14HasCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\ntime_stamp\x18\x04 \x01(\x04\"J\n\x0c\x42oolResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\r\n\x05value\x18\x02 \x01(\x08\"L\n\x0eStringResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\r\n\x05value\x18\x02 \x01(\t\"\xaf\x01\n\x19\x44\x65scribeCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x14\n\x0c\x63ollectionID\x18\x04 \x01(\x03\x12\x12\n\ntime_stamp\x18\x05 \x01(\x04:\x12\xca>\x0f\x08\x01\x10\x03\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x87\x05\n\x1a\x44\x65scribeCollectionResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x35\n\x06schema\x18\x02 \x01(\x0b\x32%.milvus.proto.schema.CollectionSchema\x12\x14\n\x0c\x63ollectionID\x18\x03 \x01(\x03\x12\x1d\n\x15virtual_channel_names\x18\x04 \x03(\t\x12\x1e\n\x16physical_channel_names\x18\x05 \x03(\t\x12\x19\n\x11\x63reated_timestamp\x18\x06 \x01(\x04\x12\x1d\n\x15\x63reated_utc_timestamp\x18\x07 \x01(\x04\x12\x12\n\nshards_num\x18\x08 \x01(\x05\x12\x0f\n\x07\x61liases\x18\t \x03(\t\x12\x39\n\x0fstart_positions\x18\n \x03(\x0b\x32 .milvus.proto.common.KeyDataPair\x12@\n\x11\x63onsistency_level\x18\x0b \x01(\x0e\x32%.milvus.proto.common.ConsistencyLevel\x12\x17\n\x0f\x63ollection_name\x18\x0c \x01(\t\x12\x35\n\nproperties\x18\r \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x0f\n\x07\x64\x62_name\x18\x0e \x01(\t\x12\x16\n\x0enum_partitions\x18\x0f \x01(\x03\x12\r\n\x05\x64\x62_id\x18\x10 \x01(\x03\x12\x14\n\x0crequest_time\x18\x11 \x01(\x04\x12\x18\n\x10update_timestamp\x18\x12 \x01(\x04\x12\x1c\n\x14update_timestamp_str\x18\x13 \x01(\t\"\xf2\x02\n\x15LoadCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0ereplica_number\x18\x04 \x01(\x05\x12\x17\n\x0fresource_groups\x18\x05 \x03(\t\x12\x0f\n\x07refresh\x18\x06 \x01(\x08\x12\x13\n\x0bload_fields\x18\x07 \x03(\t\x12\x1f\n\x17skip_load_dynamic_field\x18\x08 \x01(\x08\x12O\n\x0bload_params\x18\t \x03(\x0b\x32:.milvus.proto.milvus.LoadCollectionRequest.LoadParamsEntry\x1a\x31\n\x0fLoadParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01:\x07\xca>\x04\x10\x05\x18\x03\"y\n\x18ReleaseCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t:\x07\xca>\x04\x10\x06\x18\x03\"\xab\x01\n\x14GetStatisticsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x17\n\x0fpartition_names\x18\x04 \x03(\t\x12\x1b\n\x13guarantee_timestamp\x18\x05 \x01(\x04:\x07\xca>\x04\x10\n\x18\x03\"v\n\x15GetStatisticsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x30\n\x05stats\x18\x02 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\"\x7f\n\x1eGetCollectionStatisticsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t:\x07\xca>\x04\x10\n\x18\x03\"\x80\x01\n\x1fGetCollectionStatisticsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x30\n\x05stats\x18\x02 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\"\xb4\x01\n\x16ShowCollectionsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x12\n\ntime_stamp\x18\x03 \x01(\x04\x12+\n\x04type\x18\x04 \x01(\x0e\x32\x1d.milvus.proto.milvus.ShowType\x12\x1c\n\x10\x63ollection_names\x18\x05 \x03(\tB\x02\x18\x01\"\xf7\x01\n\x17ShowCollectionsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x18\n\x10\x63ollection_names\x18\x02 \x03(\t\x12\x16\n\x0e\x63ollection_ids\x18\x03 \x03(\x03\x12\x1a\n\x12\x63reated_timestamps\x18\x04 \x03(\x04\x12\x1e\n\x16\x63reated_utc_timestamps\x18\x05 \x03(\x04\x12 \n\x14inMemory_percentages\x18\x06 \x03(\x03\x42\x02\x18\x01\x12\x1f\n\x17query_service_available\x18\x07 \x03(\x08\"\x8f\x01\n\x16\x43reatePartitionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0epartition_name\x18\x04 \x01(\t:\x07\xca>\x04\x10\'\x18\x03\"\x8d\x01\n\x14\x44ropPartitionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0epartition_name\x18\x04 \x01(\t:\x07\xca>\x04\x10(\x18\x03\"\x8c\x01\n\x13HasPartitionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0epartition_name\x18\x04 \x01(\t:\x07\xca>\x04\x10*\x18\x03\"\x8b\x03\n\x15LoadPartitionsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x17\n\x0fpartition_names\x18\x04 \x03(\t\x12\x16\n\x0ereplica_number\x18\x05 \x01(\x05\x12\x17\n\x0fresource_groups\x18\x06 \x03(\t\x12\x0f\n\x07refresh\x18\x07 \x01(\x08\x12\x13\n\x0bload_fields\x18\x08 \x03(\t\x12\x1f\n\x17skip_load_dynamic_field\x18\t \x01(\x08\x12O\n\x0bload_params\x18\n \x03(\x0b\x32:.milvus.proto.milvus.LoadPartitionsRequest.LoadParamsEntry\x1a\x31\n\x0fLoadParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01:\x07\xca>\x04\x10\x05\x18\x03\"\x92\x01\n\x18ReleasePartitionsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x17\n\x0fpartition_names\x18\x04 \x03(\t:\x07\xca>\x04\x10\x06\x18\x03\"\x8d\x01\n\x1dGetPartitionStatisticsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0epartition_name\x18\x04 \x01(\t\"\x7f\n\x1eGetPartitionStatisticsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x30\n\x05stats\x18\x02 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\"\xd6\x01\n\x15ShowPartitionsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x14\n\x0c\x63ollectionID\x18\x04 \x01(\x03\x12\x17\n\x0fpartition_names\x18\x05 \x03(\t\x12/\n\x04type\x18\x06 \x01(\x0e\x32\x1d.milvus.proto.milvus.ShowTypeB\x02\x18\x01:\x07\xca>\x04\x10)\x18\x03\"\xd2\x01\n\x16ShowPartitionsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x17\n\x0fpartition_names\x18\x02 \x03(\t\x12\x14\n\x0cpartitionIDs\x18\x03 \x03(\x03\x12\x1a\n\x12\x63reated_timestamps\x18\x04 \x03(\x04\x12\x1e\n\x16\x63reated_utc_timestamps\x18\x05 \x03(\x04\x12 \n\x14inMemory_percentages\x18\x06 \x03(\x03\x42\x02\x18\x01\"m\n\x16\x44\x65scribeSegmentRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x14\n\x0c\x63ollectionID\x18\x02 \x01(\x03\x12\x11\n\tsegmentID\x18\x03 \x01(\x03\"\x8f\x01\n\x17\x44\x65scribeSegmentResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x0f\n\x07indexID\x18\x02 \x01(\x03\x12\x0f\n\x07\x62uildID\x18\x03 \x01(\x03\x12\x14\n\x0c\x65nable_index\x18\x04 \x01(\x08\x12\x0f\n\x07\x66ieldID\x18\x05 \x01(\x03\"l\n\x13ShowSegmentsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x14\n\x0c\x63ollectionID\x18\x02 \x01(\x03\x12\x13\n\x0bpartitionID\x18\x03 \x01(\x03\"W\n\x14ShowSegmentsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x12\n\nsegmentIDs\x18\x02 \x03(\x03\"\xd4\x01\n\x12\x43reateIndexRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\nfield_name\x18\x04 \x01(\t\x12\x37\n\x0c\x65xtra_params\x18\x05 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x12\n\nindex_name\x18\x06 \x01(\t:\x07\xca>\x04\x10\x0b\x18\x03\"\xd4\x01\n\x11\x41lterIndexRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\nindex_name\x18\x04 \x01(\t\x12\x37\n\x0c\x65xtra_params\x18\x05 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x13\n\x0b\x64\x65lete_keys\x18\x06 \x03(\t:\x07\xca>\x04\x10\x0b\x18\x03\"\xb0\x01\n\x14\x44\x65scribeIndexRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\nfield_name\x18\x04 \x01(\t\x12\x12\n\nindex_name\x18\x05 \x01(\t\x12\x11\n\ttimestamp\x18\x06 \x01(\x04:\x07\xca>\x04\x10\x0c\x18\x03\"\xcb\x02\n\x10IndexDescription\x12\x12\n\nindex_name\x18\x01 \x01(\t\x12\x0f\n\x07indexID\x18\x02 \x01(\x03\x12\x31\n\x06params\x18\x03 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x12\n\nfield_name\x18\x04 \x01(\t\x12\x14\n\x0cindexed_rows\x18\x05 \x01(\x03\x12\x12\n\ntotal_rows\x18\x06 \x01(\x03\x12.\n\x05state\x18\x07 \x01(\x0e\x32\x1f.milvus.proto.common.IndexState\x12\x1f\n\x17index_state_fail_reason\x18\x08 \x01(\t\x12\x1a\n\x12pending_index_rows\x18\t \x01(\x03\x12\x19\n\x11min_index_version\x18\n \x01(\x05\x12\x19\n\x11max_index_version\x18\x0b \x01(\x05\"\x87\x01\n\x15\x44\x65scribeIndexResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x41\n\x12index_descriptions\x18\x02 \x03(\x0b\x32%.milvus.proto.milvus.IndexDescription\"\xa5\x01\n\x1cGetIndexBuildProgressRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\nfield_name\x18\x04 \x01(\t\x12\x12\n\nindex_name\x18\x05 \x01(\t:\x07\xca>\x04\x10\x0c\x18\x03\"v\n\x1dGetIndexBuildProgressResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x14\n\x0cindexed_rows\x18\x02 \x01(\x03\x12\x12\n\ntotal_rows\x18\x03 \x01(\x03\"\x9d\x01\n\x14GetIndexStateRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\nfield_name\x18\x04 \x01(\t\x12\x12\n\nindex_name\x18\x05 \x01(\t:\x07\xca>\x04\x10\x0c\x18\x03\"\x89\x01\n\x15GetIndexStateResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12.\n\x05state\x18\x02 \x01(\x0e\x32\x1f.milvus.proto.common.IndexState\x12\x13\n\x0b\x66\x61il_reason\x18\x03 \x01(\t\"\x99\x01\n\x10\x44ropIndexRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\nfield_name\x18\x04 \x01(\t\x12\x12\n\nindex_name\x18\x05 \x01(\t:\x07\xca>\x04\x10\r\x18\x03\"\xfa\x01\n\rInsertRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0epartition_name\x18\x04 \x01(\t\x12\x33\n\x0b\x66ields_data\x18\x05 \x03(\x0b\x32\x1e.milvus.proto.schema.FieldData\x12\x11\n\thash_keys\x18\x06 \x03(\r\x12\x10\n\x08num_rows\x18\x07 \x01(\r\x12\x18\n\x10schema_timestamp\x18\x08 \x01(\x04:\x07\xca>\x04\x10\x08\x18\x03\"\xa0\x01\n\x19\x41\x64\x64\x43ollectionFieldRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x14\n\x0c\x63ollectionID\x18\x04 \x01(\x03\x12\x0e\n\x06schema\x18\x05 \x01(\x0c:\x07\xca>\x04\x10G\x18\x03\"\xfa\x01\n\rUpsertRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0epartition_name\x18\x04 \x01(\t\x12\x33\n\x0b\x66ields_data\x18\x05 \x03(\x0b\x32\x1e.milvus.proto.schema.FieldData\x12\x11\n\thash_keys\x18\x06 \x03(\r\x12\x10\n\x08num_rows\x18\x07 \x01(\r\x12\x18\n\x10schema_timestamp\x18\x08 \x01(\x04:\x07\xca>\x04\x10\x19\x18\x03\"\xf0\x01\n\x0eMutationResult\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12%\n\x03IDs\x18\x02 \x01(\x0b\x32\x18.milvus.proto.schema.IDs\x12\x12\n\nsucc_index\x18\x03 \x03(\r\x12\x11\n\terr_index\x18\x04 \x03(\r\x12\x14\n\x0c\x61\x63knowledged\x18\x05 \x01(\x08\x12\x12\n\ninsert_cnt\x18\x06 \x01(\x03\x12\x12\n\ndelete_cnt\x18\x07 \x01(\x03\x12\x12\n\nupsert_cnt\x18\x08 \x01(\x03\x12\x11\n\ttimestamp\x18\t \x01(\x04\"\xa2\x03\n\rDeleteRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0epartition_name\x18\x04 \x01(\t\x12\x0c\n\x04\x65xpr\x18\x05 \x01(\t\x12\x11\n\thash_keys\x18\x06 \x03(\r\x12@\n\x11\x63onsistency_level\x18\x07 \x01(\x0e\x32%.milvus.proto.common.ConsistencyLevel\x12X\n\x14\x65xpr_template_values\x18\x08 \x03(\x0b\x32:.milvus.proto.milvus.DeleteRequest.ExprTemplateValuesEntry\x1a]\n\x17\x45xprTemplateValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32\".milvus.proto.schema.TemplateValue:\x02\x38\x01:\x07\xca>\x04\x10\t\x18\x03\"\xec\x02\n\x10SubSearchRequest\x12\x0b\n\x03\x64sl\x18\x01 \x01(\t\x12\x19\n\x11placeholder_group\x18\x02 \x01(\x0c\x12.\n\x08\x64sl_type\x18\x03 \x01(\x0e\x32\x1c.milvus.proto.common.DslType\x12\x38\n\rsearch_params\x18\x04 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\n\n\x02nq\x18\x05 \x01(\x03\x12[\n\x14\x65xpr_template_values\x18\x06 \x03(\x0b\x32=.milvus.proto.milvus.SubSearchRequest.ExprTemplateValuesEntry\x1a]\n\x17\x45xprTemplateValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32\".milvus.proto.schema.TemplateValue:\x02\x38\x01\"\xc1\x06\n\rSearchRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x17\n\x0fpartition_names\x18\x04 \x03(\t\x12\x0b\n\x03\x64sl\x18\x05 \x01(\t\x12\x19\n\x11placeholder_group\x18\x06 \x01(\x0c\x12.\n\x08\x64sl_type\x18\x07 \x01(\x0e\x32\x1c.milvus.proto.common.DslType\x12\x15\n\routput_fields\x18\x08 \x03(\t\x12\x38\n\rsearch_params\x18\t \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x18\n\x10travel_timestamp\x18\n \x01(\x04\x12\x1b\n\x13guarantee_timestamp\x18\x0b \x01(\x04\x12\n\n\x02nq\x18\x0c \x01(\x03\x12\x1b\n\x13not_return_all_meta\x18\r \x01(\x08\x12@\n\x11\x63onsistency_level\x18\x0e \x01(\x0e\x32%.milvus.proto.common.ConsistencyLevel\x12\x1f\n\x17use_default_consistency\x18\x0f \x01(\x08\x12\x1e\n\x16search_by_primary_keys\x18\x10 \x01(\x08\x12\x37\n\x08sub_reqs\x18\x11 \x03(\x0b\x32%.milvus.proto.milvus.SubSearchRequest\x12X\n\x14\x65xpr_template_values\x18\x12 \x03(\x0b\x32:.milvus.proto.milvus.SearchRequest.ExprTemplateValuesEntry\x12:\n\x0e\x66unction_score\x18\x13 \x01(\x0b\x32\".milvus.proto.schema.FunctionScore\x1a]\n\x17\x45xprTemplateValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32\".milvus.proto.schema.TemplateValue:\x02\x38\x01:\x07\xca>\x04\x10\x0e\x18\x03\"5\n\x04Hits\x12\x0b\n\x03IDs\x18\x01 \x03(\x03\x12\x10\n\x08row_data\x18\x02 \x03(\x0c\x12\x0e\n\x06scores\x18\x03 \x03(\x02\"\xa1\x01\n\rSearchResults\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x36\n\x07results\x18\x02 \x01(\x0b\x32%.milvus.proto.schema.SearchResultData\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\nsession_ts\x18\x04 \x01(\x04\"\x85\x04\n\x13HybridSearchRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x17\n\x0fpartition_names\x18\x04 \x03(\t\x12\x34\n\x08requests\x18\x05 \x03(\x0b\x32\".milvus.proto.milvus.SearchRequest\x12\x36\n\x0brank_params\x18\x06 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x18\n\x10travel_timestamp\x18\x07 \x01(\x04\x12\x1b\n\x13guarantee_timestamp\x18\x08 \x01(\x04\x12\x1b\n\x13not_return_all_meta\x18\t \x01(\x08\x12\x15\n\routput_fields\x18\n \x03(\t\x12@\n\x11\x63onsistency_level\x18\x0b \x01(\x0e\x32%.milvus.proto.common.ConsistencyLevel\x12\x1f\n\x17use_default_consistency\x18\x0c \x01(\x08\x12:\n\x0e\x66unction_score\x18\r \x01(\x0b\x32\".milvus.proto.schema.FunctionScore:\x07\xca>\x04\x10\x0e\x18\x03\"n\n\x0c\x46lushRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x18\n\x10\x63ollection_names\x18\x03 \x03(\t:\x07\xca>\x04\x10\x0f \x03\"\xb6\x06\n\rFlushResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12G\n\x0b\x63oll_segIDs\x18\x03 \x03(\x0b\x32\x32.milvus.proto.milvus.FlushResponse.CollSegIDsEntry\x12R\n\x11\x66lush_coll_segIDs\x18\x04 \x03(\x0b\x32\x37.milvus.proto.milvus.FlushResponse.FlushCollSegIDsEntry\x12N\n\x0f\x63oll_seal_times\x18\x05 \x03(\x0b\x32\x35.milvus.proto.milvus.FlushResponse.CollSealTimesEntry\x12J\n\rcoll_flush_ts\x18\x06 \x03(\x0b\x32\x33.milvus.proto.milvus.FlushResponse.CollFlushTsEntry\x12G\n\x0b\x63hannel_cps\x18\x07 \x03(\x0b\x32\x32.milvus.proto.milvus.FlushResponse.ChannelCpsEntry\x1aQ\n\x0f\x43ollSegIDsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12-\n\x05value\x18\x02 \x01(\x0b\x32\x1e.milvus.proto.schema.LongArray:\x02\x38\x01\x1aV\n\x14\x46lushCollSegIDsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12-\n\x05value\x18\x02 \x01(\x0b\x32\x1e.milvus.proto.schema.LongArray:\x02\x38\x01\x1a\x34\n\x12\x43ollSealTimesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\x1a\x32\n\x10\x43ollFlushTsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x04:\x02\x38\x01\x1aP\n\x0f\x43hannelCpsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12,\n\x05value\x18\x02 \x01(\x0b\x32\x1d.milvus.proto.msg.MsgPosition:\x02\x38\x01\"\xd3\x04\n\x0cQueryRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x0c\n\x04\x65xpr\x18\x04 \x01(\t\x12\x15\n\routput_fields\x18\x05 \x03(\t\x12\x17\n\x0fpartition_names\x18\x06 \x03(\t\x12\x18\n\x10travel_timestamp\x18\x07 \x01(\x04\x12\x1b\n\x13guarantee_timestamp\x18\x08 \x01(\x04\x12\x37\n\x0cquery_params\x18\t \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x1b\n\x13not_return_all_meta\x18\n \x01(\x08\x12@\n\x11\x63onsistency_level\x18\x0b \x01(\x0e\x32%.milvus.proto.common.ConsistencyLevel\x12\x1f\n\x17use_default_consistency\x18\x0c \x01(\x08\x12W\n\x14\x65xpr_template_values\x18\r \x03(\x0b\x32\x39.milvus.proto.milvus.QueryRequest.ExprTemplateValuesEntry\x1a]\n\x17\x45xprTemplateValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32\".milvus.proto.schema.TemplateValue:\x02\x38\x01:\x07\xca>\x04\x10\x10\x18\x03\"\xd0\x01\n\x0cQueryResults\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x33\n\x0b\x66ields_data\x18\x02 \x03(\x0b\x32\x1e.milvus.proto.schema.FieldData\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x15\n\routput_fields\x18\x04 \x03(\t\x12\x12\n\nsession_ts\x18\x05 \x01(\x04\x12\x1a\n\x12primary_field_name\x18\x06 \x01(\t\"R\n\x0bQueryCursor\x12\x12\n\nsession_ts\x18\x01 \x01(\x04\x12\x10\n\x06str_pk\x18\x02 \x01(\tH\x00\x12\x10\n\x06int_pk\x18\x03 \x01(\x03H\x00\x42\x0b\n\tcursor_pk\"}\n\tVectorIDs\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x12\n\nfield_name\x18\x02 \x01(\t\x12*\n\x08id_array\x18\x03 \x01(\x0b\x32\x18.milvus.proto.schema.IDs\x12\x17\n\x0fpartition_names\x18\x04 \x03(\t\"\x83\x01\n\x0cVectorsArray\x12\x32\n\x08id_array\x18\x01 \x01(\x0b\x32\x1e.milvus.proto.milvus.VectorIDsH\x00\x12\x36\n\ndata_array\x18\x02 \x01(\x0b\x32 .milvus.proto.schema.VectorFieldH\x00\x42\x07\n\x05\x61rray\"\xdd\x01\n\x13\x43\x61lcDistanceRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x32\n\x07op_left\x18\x02 \x01(\x0b\x32!.milvus.proto.milvus.VectorsArray\x12\x33\n\x08op_right\x18\x03 \x01(\x0b\x32!.milvus.proto.milvus.VectorsArray\x12\x31\n\x06params\x18\x04 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\"\xb5\x01\n\x13\x43\x61lcDistanceResults\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x31\n\x08int_dist\x18\x02 \x01(\x0b\x32\x1d.milvus.proto.schema.IntArrayH\x00\x12\x35\n\nfloat_dist\x18\x03 \x01(\x0b\x32\x1f.milvus.proto.schema.FloatArrayH\x00\x42\x07\n\x05\x61rray\"b\n\x0f\x46lushAllRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t:\x12\xca>\x0f\x08\x01\x10&\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"U\n\x10\x46lushAllResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x14\n\x0c\x66lush_all_ts\x18\x02 \x01(\x04\"\xf7\x01\n\x15PersistentSegmentInfo\x12\x11\n\tsegmentID\x18\x01 \x01(\x03\x12\x14\n\x0c\x63ollectionID\x18\x02 \x01(\x03\x12\x13\n\x0bpartitionID\x18\x03 \x01(\x03\x12\x10\n\x08num_rows\x18\x04 \x01(\x03\x12\x30\n\x05state\x18\x05 \x01(\x0e\x32!.milvus.proto.common.SegmentState\x12\x30\n\x05level\x18\x06 \x01(\x0e\x32!.milvus.proto.common.SegmentLevel\x12\x11\n\tis_sorted\x18\x07 \x01(\x08\x12\x17\n\x0fstorage_version\x18\x08 \x01(\x03\"u\n\x1fGetPersistentSegmentInfoRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0e\n\x06\x64\x62Name\x18\x02 \x01(\t\x12\x16\n\x0e\x63ollectionName\x18\x03 \x01(\t\"\x8a\x01\n GetPersistentSegmentInfoResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x39\n\x05infos\x18\x02 \x03(\x0b\x32*.milvus.proto.milvus.PersistentSegmentInfo\"\xce\x02\n\x10QuerySegmentInfo\x12\x11\n\tsegmentID\x18\x01 \x01(\x03\x12\x14\n\x0c\x63ollectionID\x18\x02 \x01(\x03\x12\x13\n\x0bpartitionID\x18\x03 \x01(\x03\x12\x10\n\x08mem_size\x18\x04 \x01(\x03\x12\x10\n\x08num_rows\x18\x05 \x01(\x03\x12\x12\n\nindex_name\x18\x06 \x01(\t\x12\x0f\n\x07indexID\x18\x07 \x01(\x03\x12\x12\n\x06nodeID\x18\x08 \x01(\x03\x42\x02\x18\x01\x12\x30\n\x05state\x18\t \x01(\x0e\x32!.milvus.proto.common.SegmentState\x12\x0f\n\x07nodeIds\x18\n \x03(\x03\x12\x30\n\x05level\x18\x0b \x01(\x0e\x32!.milvus.proto.common.SegmentLevel\x12\x11\n\tis_sorted\x18\x0c \x01(\x08\x12\x17\n\x0fstorage_version\x18\r \x01(\x03\"p\n\x1aGetQuerySegmentInfoRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0e\n\x06\x64\x62Name\x18\x02 \x01(\t\x12\x16\n\x0e\x63ollectionName\x18\x03 \x01(\t\"\x80\x01\n\x1bGetQuerySegmentInfoResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x34\n\x05infos\x18\x02 \x03(\x0b\x32%.milvus.proto.milvus.QuerySegmentInfo\"$\n\x0c\x44ummyRequest\x12\x14\n\x0crequest_type\x18\x01 \x01(\t\"!\n\rDummyResponse\x12\x10\n\x08response\x18\x01 \x01(\t\"\x15\n\x13RegisterLinkRequest\"r\n\x14RegisterLinkResponse\x12-\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.Address\x12+\n\x06status\x18\x02 \x01(\x0b\x32\x1b.milvus.proto.common.Status\"P\n\x11GetMetricsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07request\x18\x02 \x01(\t\"k\n\x12GetMetricsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x10\n\x08response\x18\x02 \x01(\t\x12\x16\n\x0e\x63omponent_name\x18\x03 \x01(\t\"\x98\x01\n\rComponentInfo\x12\x0e\n\x06nodeID\x18\x01 \x01(\x03\x12\x0c\n\x04role\x18\x02 \x01(\t\x12\x32\n\nstate_code\x18\x03 \x01(\x0e\x32\x1e.milvus.proto.common.StateCode\x12\x35\n\nextra_info\x18\x04 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\"\xb2\x01\n\x0f\x43omponentStates\x12\x31\n\x05state\x18\x01 \x01(\x0b\x32\".milvus.proto.milvus.ComponentInfo\x12?\n\x13subcomponent_states\x18\x02 \x03(\x0b\x32\".milvus.proto.milvus.ComponentInfo\x12+\n\x06status\x18\x03 \x01(\x0b\x32\x1b.milvus.proto.common.Status\"\x1b\n\x19GetComponentStatesRequest\"\xb6\x01\n\x12LoadBalanceRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x12\n\nsrc_nodeID\x18\x02 \x01(\x03\x12\x13\n\x0b\x64st_nodeIDs\x18\x03 \x03(\x03\x12\x19\n\x11sealed_segmentIDs\x18\x04 \x03(\x03\x12\x16\n\x0e\x63ollectionName\x18\x05 \x01(\t\x12\x0f\n\x07\x64\x62_name\x18\x06 \x01(\t:\x07\xca>\x04\x10\x11\x18\x05\"\xcb\x01\n\x17ManualCompactionRequest\x12\x14\n\x0c\x63ollectionID\x18\x01 \x01(\x03\x12\x12\n\ntimetravel\x18\x02 \x01(\x04\x12\x17\n\x0fmajorCompaction\x18\x03 \x01(\x08\x12\x17\n\x0f\x63ollection_name\x18\x04 \x01(\t\x12\x0f\n\x07\x64\x62_name\x18\x05 \x01(\t\x12\x14\n\x0cpartition_id\x18\x06 \x01(\x03\x12\x0f\n\x07\x63hannel\x18\x07 \x01(\t\x12\x13\n\x0bsegment_ids\x18\x08 \x03(\x03:\x07\xca>\x04\x10\x07\x18\x04\"z\n\x18ManualCompactionResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x14\n\x0c\x63ompactionID\x18\x02 \x01(\x03\x12\x1b\n\x13\x63ompactionPlanCount\x18\x03 \x01(\x05\"1\n\x19GetCompactionStateRequest\x12\x14\n\x0c\x63ompactionID\x18\x01 \x01(\x03\"\xdd\x01\n\x1aGetCompactionStateResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x33\n\x05state\x18\x02 \x01(\x0e\x32$.milvus.proto.common.CompactionState\x12\x17\n\x0f\x65xecutingPlanNo\x18\x03 \x01(\x03\x12\x15\n\rtimeoutPlanNo\x18\x04 \x01(\x03\x12\x17\n\x0f\x63ompletedPlanNo\x18\x05 \x01(\x03\x12\x14\n\x0c\x66\x61iledPlanNo\x18\x06 \x01(\x03\"1\n\x19GetCompactionPlansRequest\x12\x14\n\x0c\x63ompactionID\x18\x01 \x01(\x03\"\xbc\x01\n\x1aGetCompactionPlansResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x33\n\x05state\x18\x02 \x01(\x0e\x32$.milvus.proto.common.CompactionState\x12<\n\nmergeInfos\x18\x03 \x03(\x0b\x32(.milvus.proto.milvus.CompactionMergeInfo\"6\n\x13\x43ompactionMergeInfo\x12\x0f\n\x07sources\x18\x01 \x03(\x03\x12\x0e\n\x06target\x18\x02 \x01(\x03\"o\n\x14GetFlushStateRequest\x12\x12\n\nsegmentIDs\x18\x01 \x03(\x03\x12\x10\n\x08\x66lush_ts\x18\x02 \x01(\x04\x12\x0f\n\x07\x64\x62_name\x18\x03 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x04 \x01(\t:\x07\xca>\x04\x10+\x18\x04\"U\n\x15GetFlushStateResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x0f\n\x07\x66lushed\x18\x02 \x01(\x08\"l\n\x17GetFlushAllStateRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x14\n\x0c\x66lush_all_ts\x18\x02 \x01(\x04\x12\x0f\n\x07\x64\x62_name\x18\x03 \x01(\t\"X\n\x18GetFlushAllStateResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x0f\n\x07\x66lushed\x18\x02 \x01(\x08\"\xe0\x01\n\rImportRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x16\n\x0epartition_name\x18\x02 \x01(\t\x12\x15\n\rchannel_names\x18\x03 \x03(\t\x12\x11\n\trow_based\x18\x04 \x01(\x08\x12\r\n\x05\x66iles\x18\x05 \x03(\t\x12\x32\n\x07options\x18\x06 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x0f\n\x07\x64\x62_name\x18\x07 \x01(\t\x12\x17\n\x0f\x63lustering_info\x18\x08 \x01(\x0c:\x07\xca>\x04\x10\x12\x18\x01\"L\n\x0eImportResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\r\n\x05tasks\x18\x02 \x03(\x03\"%\n\x15GetImportStateRequest\x12\x0c\n\x04task\x18\x01 \x01(\x03\"\x97\x02\n\x16GetImportStateResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12/\n\x05state\x18\x02 \x01(\x0e\x32 .milvus.proto.common.ImportState\x12\x11\n\trow_count\x18\x03 \x01(\x03\x12\x0f\n\x07id_list\x18\x04 \x03(\x03\x12\x30\n\x05infos\x18\x05 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\n\n\x02id\x18\x06 \x01(\x03\x12\x15\n\rcollection_id\x18\x07 \x01(\x03\x12\x13\n\x0bsegment_ids\x18\x08 \x03(\x03\x12\x11\n\tcreate_ts\x18\t \x01(\x03\"Q\n\x16ListImportTasksRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\r\n\x05limit\x18\x02 \x01(\x03\x12\x0f\n\x07\x64\x62_name\x18\x03 \x01(\t\"\x82\x01\n\x17ListImportTasksResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12:\n\x05tasks\x18\x02 \x03(\x0b\x32+.milvus.proto.milvus.GetImportStateResponse\"\x9a\x01\n\x12GetReplicasRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x14\n\x0c\x63ollectionID\x18\x02 \x01(\x03\x12\x18\n\x10with_shard_nodes\x18\x03 \x01(\x08\x12\x17\n\x0f\x63ollection_name\x18\x04 \x01(\t\x12\x0f\n\x07\x64\x62_name\x18\x05 \x01(\t\"v\n\x13GetReplicasResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x32\n\x08replicas\x18\x02 \x03(\x0b\x32 .milvus.proto.milvus.ReplicaInfo\"\xc1\x02\n\x0bReplicaInfo\x12\x11\n\treplicaID\x18\x01 \x01(\x03\x12\x14\n\x0c\x63ollectionID\x18\x02 \x01(\x03\x12\x15\n\rpartition_ids\x18\x03 \x03(\x03\x12\x39\n\x0eshard_replicas\x18\x04 \x03(\x0b\x32!.milvus.proto.milvus.ShardReplica\x12\x10\n\x08node_ids\x18\x05 \x03(\x03\x12\x1b\n\x13resource_group_name\x18\x06 \x01(\t\x12P\n\x11num_outbound_node\x18\x07 \x03(\x0b\x32\x35.milvus.proto.milvus.ReplicaInfo.NumOutboundNodeEntry\x1a\x36\n\x14NumOutboundNodeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\"`\n\x0cShardReplica\x12\x10\n\x08leaderID\x18\x01 \x01(\x03\x12\x13\n\x0bleader_addr\x18\x02 \x01(\t\x12\x17\n\x0f\x64m_channel_name\x18\x03 \x01(\t\x12\x10\n\x08node_ids\x18\x04 \x03(\x03\"\xbe\x01\n\x17\x43reateCredentialRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x10\n\x08username\x18\x02 \x01(\t\x12\x10\n\x08password\x18\x03 \x01(\t\x12\x1e\n\x16\x63reated_utc_timestamps\x18\x04 \x01(\x04\x12\x1f\n\x17modified_utc_timestamps\x18\x05 \x01(\x04:\x12\xca>\x0f\x08\x01\x10\x13\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xcd\x01\n\x17UpdateCredentialRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x10\n\x08username\x18\x02 \x01(\t\x12\x13\n\x0boldPassword\x18\x03 \x01(\t\x12\x13\n\x0bnewPassword\x18\x04 \x01(\t\x12\x1e\n\x16\x63reated_utc_timestamps\x18\x05 \x01(\x04\x12\x1f\n\x17modified_utc_timestamps\x18\x06 \x01(\x04:\t\xca>\x06\x08\x02\x10\x14\x18\x02\"k\n\x17\x44\x65leteCredentialRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x10\n\x08username\x18\x02 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x15\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"W\n\x15ListCredUsersResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x11\n\tusernames\x18\x02 \x03(\t\"V\n\x14ListCredUsersRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase:\x12\xca>\x0f\x08\x01\x10\x16\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x1a\n\nRoleEntity\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x1a\n\nUserEntity\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x84\x01\n\x11\x43reateRoleRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12/\n\x06\x65ntity\x18\x02 \x01(\x0b\x32\x1f.milvus.proto.milvus.RoleEntity:\x12\xca>\x0f\x08\x01\x10\x13\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"x\n\x0f\x44ropRoleRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x11\n\trole_name\x18\x02 \x01(\t\x12\x12\n\nforce_drop\x18\x03 \x01(\x08:\x12\xca>\x0f\x08\x01\x10\x15\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"q\n\x1b\x43reatePrivilegeGroupRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x12\n\ngroup_name\x18\x02 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x38\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"o\n\x19\x44ropPrivilegeGroupRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x12\n\ngroup_name\x18\x02 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x39\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\\\n\x1aListPrivilegeGroupsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase:\x12\xca>\x0f\x08\x01\x10:\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x8d\x01\n\x1bListPrivilegeGroupsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x41\n\x10privilege_groups\x18\x02 \x03(\x0b\x32\'.milvus.proto.milvus.PrivilegeGroupInfo\"\xea\x01\n\x1cOperatePrivilegeGroupRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x12\n\ngroup_name\x18\x02 \x01(\t\x12\x38\n\nprivileges\x18\x03 \x03(\x0b\x32$.milvus.proto.milvus.PrivilegeEntity\x12<\n\x04type\x18\x04 \x01(\x0e\x32..milvus.proto.milvus.OperatePrivilegeGroupType:\x12\xca>\x0f\x08\x01\x10;\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xb5\x01\n\x16OperateUserRoleRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x10\n\x08username\x18\x02 \x01(\t\x12\x11\n\trole_name\x18\x03 \x01(\t\x12\x36\n\x04type\x18\x04 \x01(\x0e\x32(.milvus.proto.milvus.OperateUserRoleType:\x12\xca>\x0f\x08\x01\x10\x17\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"b\n\x12PrivilegeGroupInfo\x12\x12\n\ngroup_name\x18\x01 \x01(\t\x12\x38\n\nprivileges\x18\x02 \x03(\x0b\x32$.milvus.proto.milvus.PrivilegeEntity\"\x9d\x01\n\x11SelectRoleRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12-\n\x04role\x18\x02 \x01(\x0b\x32\x1f.milvus.proto.milvus.RoleEntity\x12\x19\n\x11include_user_info\x18\x03 \x01(\x08:\x12\xca>\x0f\x08\x01\x10\x16\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"k\n\nRoleResult\x12-\n\x04role\x18\x01 \x01(\x0b\x32\x1f.milvus.proto.milvus.RoleEntity\x12.\n\x05users\x18\x02 \x03(\x0b\x32\x1f.milvus.proto.milvus.UserEntity\"s\n\x12SelectRoleResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x30\n\x07results\x18\x02 \x03(\x0b\x32\x1f.milvus.proto.milvus.RoleResult\"\x94\x01\n\x11SelectUserRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12-\n\x04user\x18\x02 \x01(\x0b\x32\x1f.milvus.proto.milvus.UserEntity\x12\x19\n\x11include_role_info\x18\x03 \x01(\x08:\t\xca>\x06\x08\x02\x10\x18\x18\x02\"k\n\nUserResult\x12-\n\x04user\x18\x01 \x01(\x0b\x32\x1f.milvus.proto.milvus.UserEntity\x12.\n\x05roles\x18\x02 \x03(\x0b\x32\x1f.milvus.proto.milvus.RoleEntity\"s\n\x12SelectUserResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x30\n\x07results\x18\x02 \x03(\x0b\x32\x1f.milvus.proto.milvus.UserResult\"\x1c\n\x0cObjectEntity\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x1f\n\x0fPrivilegeEntity\x12\x0c\n\x04name\x18\x01 \x01(\t\"w\n\rGrantorEntity\x12-\n\x04user\x18\x01 \x01(\x0b\x32\x1f.milvus.proto.milvus.UserEntity\x12\x37\n\tprivilege\x18\x02 \x01(\x0b\x32$.milvus.proto.milvus.PrivilegeEntity\"L\n\x14GrantPrivilegeEntity\x12\x34\n\x08\x65ntities\x18\x01 \x03(\x0b\x32\".milvus.proto.milvus.GrantorEntity\"\xca\x01\n\x0bGrantEntity\x12-\n\x04role\x18\x01 \x01(\x0b\x32\x1f.milvus.proto.milvus.RoleEntity\x12\x31\n\x06object\x18\x02 \x01(\x0b\x32!.milvus.proto.milvus.ObjectEntity\x12\x13\n\x0bobject_name\x18\x03 \x01(\t\x12\x33\n\x07grantor\x18\x04 \x01(\x0b\x32\".milvus.proto.milvus.GrantorEntity\x12\x0f\n\x07\x64\x62_name\x18\x05 \x01(\t\"\x86\x01\n\x12SelectGrantRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x30\n\x06\x65ntity\x18\x02 \x01(\x0b\x32 .milvus.proto.milvus.GrantEntity:\x12\xca>\x0f\x08\x01\x10\x16\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"v\n\x13SelectGrantResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x32\n\x08\x65ntities\x18\x02 \x03(\x0b\x32 .milvus.proto.milvus.GrantEntity\"\xd5\x01\n\x17OperatePrivilegeRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x30\n\x06\x65ntity\x18\x02 \x01(\x0b\x32 .milvus.proto.milvus.GrantEntity\x12\x37\n\x04type\x18\x03 \x01(\x0e\x32).milvus.proto.milvus.OperatePrivilegeType\x12\x0f\n\x07version\x18\x04 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x17\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xa2\x02\n\x19OperatePrivilegeV2Request\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12-\n\x04role\x18\x02 \x01(\x0b\x32\x1f.milvus.proto.milvus.RoleEntity\x12\x33\n\x07grantor\x18\x03 \x01(\x0b\x32\".milvus.proto.milvus.GrantorEntity\x12\x37\n\x04type\x18\x04 \x01(\x0e\x32).milvus.proto.milvus.OperatePrivilegeType\x12\x0f\n\x07\x64\x62_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x06 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x17\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"Z\n\x08UserInfo\x12\x0c\n\x04user\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12.\n\x05roles\x18\x03 \x03(\x0b\x32\x1f.milvus.proto.milvus.RoleEntity\"\xdd\x01\n\x08RBACMeta\x12,\n\x05users\x18\x01 \x03(\x0b\x32\x1d.milvus.proto.milvus.UserInfo\x12.\n\x05roles\x18\x02 \x03(\x0b\x32\x1f.milvus.proto.milvus.RoleEntity\x12\x30\n\x06grants\x18\x03 \x03(\x0b\x32 .milvus.proto.milvus.GrantEntity\x12\x41\n\x10privilege_groups\x18\x04 \x03(\x0b\x32\'.milvus.proto.milvus.PrivilegeGroupInfo\"W\n\x15\x42\x61\x63kupRBACMetaRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase:\x12\xca>\x0f\x08\x01\x10\x33\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"w\n\x16\x42\x61\x63kupRBACMetaResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x30\n\tRBAC_meta\x18\x02 \x01(\x0b\x32\x1d.milvus.proto.milvus.RBACMeta\"\x8a\x01\n\x16RestoreRBACMetaRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x30\n\tRBAC_meta\x18\x02 \x01(\x0b\x32\x1d.milvus.proto.milvus.RBACMeta:\x12\xca>\x0f\x08\x01\x10\x34\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x93\x01\n\x19GetLoadingProgressRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x17\n\x0f\x63ollection_name\x18\x02 \x01(\t\x12\x17\n\x0fpartition_names\x18\x03 \x03(\t\x12\x0f\n\x07\x64\x62_name\x18\x04 \x01(\t:\x07\xca>\x04\x10!\x18\x02\"u\n\x1aGetLoadingProgressResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x10\n\x08progress\x18\x02 \x01(\x03\x12\x18\n\x10refresh_progress\x18\x03 \x01(\x03\"\x8d\x01\n\x13GetLoadStateRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x17\n\x0f\x63ollection_name\x18\x02 \x01(\t\x12\x17\n\x0fpartition_names\x18\x03 \x03(\t\x12\x0f\n\x07\x64\x62_name\x18\x04 \x01(\t:\x07\xca>\x04\x10!\x18\x02\"r\n\x14GetLoadStateResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12-\n\x05state\x18\x02 \x01(\x0e\x32\x1e.milvus.proto.common.LoadState\"\x1c\n\tMilvusExt\x12\x0f\n\x07version\x18\x01 \x01(\t\"\x13\n\x11GetVersionRequest\"R\n\x12GetVersionResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x0f\n\x07version\x18\x02 \x01(\t\"\x14\n\x12\x43heckHealthRequest\"\x9d\x01\n\x13\x43heckHealthResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x11\n\tisHealthy\x18\x02 \x01(\x08\x12\x0f\n\x07reasons\x18\x03 \x03(\t\x12\x35\n\x0cquota_states\x18\x04 \x03(\x0e\x32\x1f.milvus.proto.milvus.QuotaState\"\xaa\x01\n\x1a\x43reateResourceGroupRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x16\n\x0eresource_group\x18\x02 \x01(\t\x12\x34\n\x06\x63onfig\x18\x03 \x01(\x0b\x32$.milvus.proto.rg.ResourceGroupConfig:\x12\xca>\x0f\x08\x01\x10\x1a\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x99\x02\n\x1bUpdateResourceGroupsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12]\n\x0fresource_groups\x18\x02 \x03(\x0b\x32\x44.milvus.proto.milvus.UpdateResourceGroupsRequest.ResourceGroupsEntry\x1a[\n\x13ResourceGroupsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x33\n\x05value\x18\x02 \x01(\x0b\x32$.milvus.proto.rg.ResourceGroupConfig:\x02\x38\x01:\x12\xca>\x0f\x08\x01\x10\x30\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"r\n\x18\x44ropResourceGroupRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x16\n\x0eresource_group\x18\x02 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x1b\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xa5\x01\n\x13TransferNodeRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x1d\n\x15source_resource_group\x18\x02 \x01(\t\x12\x1d\n\x15target_resource_group\x18\x03 \x01(\t\x12\x10\n\x08num_node\x18\x04 \x01(\x05:\x12\xca>\x0f\x08\x01\x10\x1e\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xd5\x01\n\x16TransferReplicaRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x1d\n\x15source_resource_group\x18\x02 \x01(\t\x12\x1d\n\x15target_resource_group\x18\x03 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x04 \x01(\t\x12\x13\n\x0bnum_replica\x18\x05 \x01(\x03\x12\x0f\n\x07\x64\x62_name\x18\x06 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x1f\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"[\n\x19ListResourceGroupsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase:\x12\xca>\x0f\x08\x01\x10\x1d\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"b\n\x1aListResourceGroupsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x17\n\x0fresource_groups\x18\x02 \x03(\t\"v\n\x1c\x44\x65scribeResourceGroupRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x16\n\x0eresource_group\x18\x02 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x1c\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x88\x01\n\x1d\x44\x65scribeResourceGroupResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12:\n\x0eresource_group\x18\x02 \x01(\x0b\x32\".milvus.proto.milvus.ResourceGroup\"\xd6\x04\n\rResourceGroup\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08\x63\x61pacity\x18\x02 \x01(\x05\x12\x1a\n\x12num_available_node\x18\x03 \x01(\x05\x12T\n\x12num_loaded_replica\x18\x04 \x03(\x0b\x32\x38.milvus.proto.milvus.ResourceGroup.NumLoadedReplicaEntry\x12R\n\x11num_outgoing_node\x18\x05 \x03(\x0b\x32\x37.milvus.proto.milvus.ResourceGroup.NumOutgoingNodeEntry\x12R\n\x11num_incoming_node\x18\x06 \x03(\x0b\x32\x37.milvus.proto.milvus.ResourceGroup.NumIncomingNodeEntry\x12\x34\n\x06\x63onfig\x18\x07 \x01(\x0b\x32$.milvus.proto.rg.ResourceGroupConfig\x12,\n\x05nodes\x18\x08 \x03(\x0b\x32\x1d.milvus.proto.common.NodeInfo\x1a\x37\n\x15NumLoadedReplicaEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\x36\n\x14NumOutgoingNodeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\x36\n\x14NumIncomingNodeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\"\x9f\x01\n\x17RenameCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x0f\n\x07oldName\x18\x03 \x01(\t\x12\x0f\n\x07newName\x18\x04 \x01(\t\x12\x11\n\tnewDBName\x18\x05 \x01(\t:\x12\xca>\x0f\x08\x01\x10\"\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xa1\x01\n\x19GetIndexStatisticsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x12\n\nindex_name\x18\x04 \x01(\t\x12\x11\n\ttimestamp\x18\x05 \x01(\x04:\x07\xca>\x04\x10\x0c\x18\x03\"\x8c\x01\n\x1aGetIndexStatisticsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x41\n\x12index_descriptions\x18\x02 \x03(\x0b\x32%.milvus.proto.milvus.IndexDescription\"r\n\x0e\x43onnectRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x34\n\x0b\x63lient_info\x18\x02 \x01(\x0b\x32\x1f.milvus.proto.common.ClientInfo\"\x88\x01\n\x0f\x43onnectResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x34\n\x0bserver_info\x18\x02 \x01(\x0b\x32\x1f.milvus.proto.common.ServerInfo\x12\x12\n\nidentifier\x18\x03 \x01(\x03\"C\n\x15\x41llocTimestampRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\"X\n\x16\x41llocTimestampResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x11\n\ttimestamp\x18\x02 \x01(\x04\"\x9f\x01\n\x15\x43reateDatabaseRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x35\n\nproperties\x18\x03 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair:\x12\xca>\x0f\x08\x01\x10#\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"f\n\x13\x44ropDatabaseRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t:\x12\xca>\x0f\x08\x01\x10$\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"B\n\x14ListDatabasesRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\"\x81\x01\n\x15ListDatabasesResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x10\n\x08\x64\x62_names\x18\x02 \x03(\t\x12\x19\n\x11\x63reated_timestamp\x18\x03 \x03(\x04\x12\x0e\n\x06\x64\x62_ids\x18\x04 \x03(\x03\"\xc2\x01\n\x14\x41lterDatabaseRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\r\n\x05\x64\x62_id\x18\x03 \x01(\t\x12\x35\n\nproperties\x18\x04 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\x12\x13\n\x0b\x64\x65lete_keys\x18\x05 \x03(\t:\x12\xca>\x0f\x08\x01\x10\x31\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"j\n\x17\x44\x65scribeDatabaseRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x32\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xb8\x01\n\x18\x44\x65scribeDatabaseResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x0c\n\x04\x64\x62ID\x18\x03 \x01(\x03\x12\x19\n\x11\x63reated_timestamp\x18\x04 \x01(\x04\x12\x35\n\nproperties\x18\x05 \x03(\x0b\x32!.milvus.proto.common.KeyValuePair\"\xf5\x01\n\x17ReplicateMessageRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x14\n\x0c\x63hannel_name\x18\x02 \x01(\t\x12\x0f\n\x07\x42\x65ginTs\x18\x03 \x01(\x04\x12\r\n\x05\x45ndTs\x18\x04 \x01(\x04\x12\x0c\n\x04Msgs\x18\x05 \x03(\x0c\x12\x35\n\x0eStartPositions\x18\x06 \x03(\x0b\x32\x1d.milvus.proto.msg.MsgPosition\x12\x33\n\x0c\x45ndPositions\x18\x07 \x03(\x0b\x32\x1d.milvus.proto.msg.MsgPosition\"Y\n\x18ReplicateMessageResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x10\n\x08position\x18\x02 \x01(\t\"b\n\x15ImportAuthPlaceholder\x12\x0f\n\x07\x64\x62_name\x18\x01 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x02 \x01(\t\x12\x16\n\x0epartition_name\x18\x03 \x01(\t:\x07\xca>\x04\x10\x12\x18\x02\"G\n GetImportProgressAuthPlaceholder\x12\x0f\n\x07\x64\x62_name\x18\x01 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x45\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"Z\n\x1aListImportsAuthPlaceholder\x12\x0f\n\x07\x64\x62_name\x18\x03 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t:\x12\xca>\x0f\x08\x01\x10\x46\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\xec\x01\n\x12RunAnalyzerRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x17\n\x0f\x61nalyzer_params\x18\x02 \x01(\t\x12\x13\n\x0bplaceholder\x18\x03 \x03(\x0c\x12\x13\n\x0bwith_detail\x18\x04 \x01(\x08\x12\x11\n\twith_hash\x18\x05 \x01(\x08\x12\x0f\n\x07\x64\x62_name\x18\x06 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x07 \x01(\t\x12\x12\n\nfield_name\x18\x08 \x01(\t\x12\x16\n\x0e\x61nalyzer_names\x18\t \x03(\t\"\x81\x01\n\rAnalyzerToken\x12\r\n\x05token\x18\x01 \x01(\t\x12\x14\n\x0cstart_offset\x18\x02 \x01(\x03\x12\x12\n\nend_offset\x18\x03 \x01(\x03\x12\x10\n\x08position\x18\x04 \x01(\x03\x12\x17\n\x0fposition_length\x18\x05 \x01(\x03\x12\x0c\n\x04hash\x18\x06 \x01(\r\"D\n\x0e\x41nalyzerResult\x12\x32\n\x06tokens\x18\x01 \x03(\x0b\x32\".milvus.proto.milvus.AnalyzerToken\"x\n\x13RunAnalyzerResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x34\n\x07results\x18\x02 \x03(\x0b\x32#.milvus.proto.milvus.AnalyzerResult\"\xcc\x01\n\x12\x41\x64\x64UserTagsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x11\n\tuser_name\x18\x02 \x01(\t\x12?\n\x04tags\x18\x03 \x03(\x0b\x32\x31.milvus.proto.milvus.AddUserTagsRequest.TagsEntry\x1a+\n\tTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01:\t\xca>\x06\x08\x02\x10\x14\x18\x02\"s\n\x15\x44\x65leteUserTagsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x11\n\tuser_name\x18\x02 \x01(\t\x12\x10\n\x08tag_keys\x18\x03 \x03(\t:\t\xca>\x06\x08\x02\x10\x14\x18\x02\"^\n\x12GetUserTagsRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x11\n\tuser_name\x18\x02 \x01(\t:\t\xca>\x06\x08\x02\x10\x18\x18\x02\"\xb1\x01\n\x13GetUserTagsResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12@\n\x04tags\x18\x02 \x03(\x0b\x32\x32.milvus.proto.milvus.GetUserTagsResponse.TagsEntry\x1a+\n\tTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"}\n\x17ListUsersWithTagRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07tag_key\x18\x02 \x01(\t\x12\x11\n\ttag_value\x18\x03 \x01(\t:\x12\xca>\x0f\x08\x02\x10\x18\x18\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\"[\n\x18ListUsersWithTagResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x12\n\nuser_names\x18\x02 \x03(\t\"\x8f\x02\n\x16\x43reateRowPolicyRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x13\n\x0bpolicy_name\x18\x04 \x01(\t\x12\x35\n\x07\x61\x63tions\x18\x05 \x03(\x0e\x32$.milvus.proto.milvus.RowPolicyAction\x12\r\n\x05roles\x18\x06 \x03(\t\x12\x12\n\nusing_expr\x18\x07 \x01(\t\x12\x12\n\ncheck_expr\x18\x08 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\t \x01(\t:\x07\xca>\x04\x10\x13\x18\x03\"\x8a\x01\n\x14\x44ropRowPolicyRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x13\n\x0bpolicy_name\x18\x04 \x01(\t:\x07\xca>\x04\x10\x15\x18\x03\"w\n\x16ListRowPoliciesRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t:\x07\xca>\x04\x10\x16\x18\x03\"\xb7\x01\n\tRowPolicy\x12\x13\n\x0bpolicy_name\x18\x01 \x01(\t\x12\x35\n\x07\x61\x63tions\x18\x02 \x03(\x0e\x32$.milvus.proto.milvus.RowPolicyAction\x12\r\n\x05roles\x18\x03 \x03(\t\x12\x12\n\nusing_expr\x18\x04 \x01(\t\x12\x12\n\ncheck_expr\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x12\n\ncreated_at\x18\x07 \x01(\x03\"\xa2\x01\n\x17ListRowPoliciesResponse\x12+\n\x06status\x18\x01 \x01(\x0b\x32\x1b.milvus.proto.common.Status\x12\x30\n\x08policies\x18\x02 \x03(\x0b\x32\x1e.milvus.proto.milvus.RowPolicy\x12\x0f\n\x07\x64\x62_name\x18\x03 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x04 \x01(\t*%\n\x08ShowType\x12\x07\n\x03\x41ll\x10\x00\x12\x0c\n\x08InMemory\x10\x01\x1a\x02\x18\x01*T\n\x19OperatePrivilegeGroupType\x12\x18\n\x14\x41\x64\x64PrivilegesToGroup\x10\x00\x12\x1d\n\x19RemovePrivilegesFromGroup\x10\x01*@\n\x13OperateUserRoleType\x12\x11\n\rAddUserToRole\x10\x00\x12\x16\n\x12RemoveUserFromRole\x10\x01*;\n\x0ePrivilegeLevel\x12\x0b\n\x07\x43luster\x10\x00\x12\x0c\n\x08\x44\x61tabase\x10\x01\x12\x0e\n\nCollection\x10\x02*-\n\x14OperatePrivilegeType\x12\t\n\x05Grant\x10\x00\x12\n\n\x06Revoke\x10\x01*l\n\nQuotaState\x12\x0b\n\x07Unknown\x10\x00\x12\x0f\n\x0bReadLimited\x10\x02\x12\x10\n\x0cWriteLimited\x10\x03\x12\x0e\n\nDenyToRead\x10\x04\x12\x0f\n\x0b\x44\x65nyToWrite\x10\x05\x12\r\n\tDenyToDDL\x10\x06*L\n\x0fRowPolicyAction\x12\t\n\x05Query\x10\x00\x12\n\n\x06Search\x10\x01\x12\n\n\x06Insert\x10\x02\x12\n\n\x06\x44\x65lete\x10\x03\x12\n\n\x06Upsert\x10\x04\x32\xffQ\n\rMilvusService\x12_\n\x10\x43reateCollection\x12,.milvus.proto.milvus.CreateCollectionRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12[\n\x0e\x44ropCollection\x12*.milvus.proto.milvus.DropCollectionRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12_\n\rHasCollection\x12).milvus.proto.milvus.HasCollectionRequest\x1a!.milvus.proto.milvus.BoolResponse\"\x00\x12[\n\x0eLoadCollection\x12*.milvus.proto.milvus.LoadCollectionRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12\x61\n\x11ReleaseCollection\x12-.milvus.proto.milvus.ReleaseCollectionRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12w\n\x12\x44\x65scribeCollection\x12..milvus.proto.milvus.DescribeCollectionRequest\x1a/.milvus.proto.milvus.DescribeCollectionResponse\"\x00\x12\x86\x01\n\x17GetCollectionStatistics\x12\x33.milvus.proto.milvus.GetCollectionStatisticsRequest\x1a\x34.milvus.proto.milvus.GetCollectionStatisticsResponse\"\x00\x12n\n\x0fShowCollections\x12+.milvus.proto.milvus.ShowCollectionsRequest\x1a,.milvus.proto.milvus.ShowCollectionsResponse\"\x00\x12]\n\x0f\x41lterCollection\x12+.milvus.proto.milvus.AlterCollectionRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12g\n\x14\x41lterCollectionField\x12\x30.milvus.proto.milvus.AlterCollectionFieldRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12]\n\x0f\x43reatePartition\x12+.milvus.proto.milvus.CreatePartitionRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12Y\n\rDropPartition\x12).milvus.proto.milvus.DropPartitionRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12]\n\x0cHasPartition\x12(.milvus.proto.milvus.HasPartitionRequest\x1a!.milvus.proto.milvus.BoolResponse\"\x00\x12[\n\x0eLoadPartitions\x12*.milvus.proto.milvus.LoadPartitionsRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12\x61\n\x11ReleasePartitions\x12-.milvus.proto.milvus.ReleasePartitionsRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12\x83\x01\n\x16GetPartitionStatistics\x12\x32.milvus.proto.milvus.GetPartitionStatisticsRequest\x1a\x33.milvus.proto.milvus.GetPartitionStatisticsResponse\"\x00\x12k\n\x0eShowPartitions\x12*.milvus.proto.milvus.ShowPartitionsRequest\x1a+.milvus.proto.milvus.ShowPartitionsResponse\"\x00\x12w\n\x12GetLoadingProgress\x12..milvus.proto.milvus.GetLoadingProgressRequest\x1a/.milvus.proto.milvus.GetLoadingProgressResponse\"\x00\x12\x65\n\x0cGetLoadState\x12(.milvus.proto.milvus.GetLoadStateRequest\x1a).milvus.proto.milvus.GetLoadStateResponse\"\x00\x12U\n\x0b\x43reateAlias\x12\'.milvus.proto.milvus.CreateAliasRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12Q\n\tDropAlias\x12%.milvus.proto.milvus.DropAliasRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12S\n\nAlterAlias\x12&.milvus.proto.milvus.AlterAliasRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12h\n\rDescribeAlias\x12).milvus.proto.milvus.DescribeAliasRequest\x1a*.milvus.proto.milvus.DescribeAliasResponse\"\x00\x12\x62\n\x0bListAliases\x12\'.milvus.proto.milvus.ListAliasesRequest\x1a(.milvus.proto.milvus.ListAliasesResponse\"\x00\x12U\n\x0b\x43reateIndex\x12\'.milvus.proto.milvus.CreateIndexRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12S\n\nAlterIndex\x12&.milvus.proto.milvus.AlterIndexRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12h\n\rDescribeIndex\x12).milvus.proto.milvus.DescribeIndexRequest\x1a*.milvus.proto.milvus.DescribeIndexResponse\"\x00\x12w\n\x12GetIndexStatistics\x12..milvus.proto.milvus.GetIndexStatisticsRequest\x1a/.milvus.proto.milvus.GetIndexStatisticsResponse\"\x00\x12k\n\rGetIndexState\x12).milvus.proto.milvus.GetIndexStateRequest\x1a*.milvus.proto.milvus.GetIndexStateResponse\"\x03\x88\x02\x01\x12\x83\x01\n\x15GetIndexBuildProgress\x12\x31.milvus.proto.milvus.GetIndexBuildProgressRequest\x1a\x32.milvus.proto.milvus.GetIndexBuildProgressResponse\"\x03\x88\x02\x01\x12Q\n\tDropIndex\x12%.milvus.proto.milvus.DropIndexRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12S\n\x06Insert\x12\".milvus.proto.milvus.InsertRequest\x1a#.milvus.proto.milvus.MutationResult\"\x00\x12S\n\x06\x44\x65lete\x12\".milvus.proto.milvus.DeleteRequest\x1a#.milvus.proto.milvus.MutationResult\"\x00\x12S\n\x06Upsert\x12\".milvus.proto.milvus.UpsertRequest\x1a#.milvus.proto.milvus.MutationResult\"\x00\x12R\n\x06Search\x12\".milvus.proto.milvus.SearchRequest\x1a\".milvus.proto.milvus.SearchResults\"\x00\x12^\n\x0cHybridSearch\x12(.milvus.proto.milvus.HybridSearchRequest\x1a\".milvus.proto.milvus.SearchResults\"\x00\x12P\n\x05\x46lush\x12!.milvus.proto.milvus.FlushRequest\x1a\".milvus.proto.milvus.FlushResponse\"\x00\x12O\n\x05Query\x12!.milvus.proto.milvus.QueryRequest\x1a!.milvus.proto.milvus.QueryResults\"\x00\x12\x64\n\x0c\x43\x61lcDistance\x12(.milvus.proto.milvus.CalcDistanceRequest\x1a(.milvus.proto.milvus.CalcDistanceResults\"\x00\x12Y\n\x08\x46lushAll\x12$.milvus.proto.milvus.FlushAllRequest\x1a%.milvus.proto.milvus.FlushAllResponse\"\x00\x12\x63\n\x12\x41\x64\x64\x43ollectionField\x12..milvus.proto.milvus.AddCollectionFieldRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12h\n\rGetFlushState\x12).milvus.proto.milvus.GetFlushStateRequest\x1a*.milvus.proto.milvus.GetFlushStateResponse\"\x00\x12q\n\x10GetFlushAllState\x12,.milvus.proto.milvus.GetFlushAllStateRequest\x1a-.milvus.proto.milvus.GetFlushAllStateResponse\"\x00\x12\x89\x01\n\x18GetPersistentSegmentInfo\x12\x34.milvus.proto.milvus.GetPersistentSegmentInfoRequest\x1a\x35.milvus.proto.milvus.GetPersistentSegmentInfoResponse\"\x00\x12z\n\x13GetQuerySegmentInfo\x12/.milvus.proto.milvus.GetQuerySegmentInfoRequest\x1a\x30.milvus.proto.milvus.GetQuerySegmentInfoResponse\"\x00\x12\x62\n\x0bGetReplicas\x12\'.milvus.proto.milvus.GetReplicasRequest\x1a(.milvus.proto.milvus.GetReplicasResponse\"\x00\x12P\n\x05\x44ummy\x12!.milvus.proto.milvus.DummyRequest\x1a\".milvus.proto.milvus.DummyResponse\"\x00\x12\x65\n\x0cRegisterLink\x12(.milvus.proto.milvus.RegisterLinkRequest\x1a).milvus.proto.milvus.RegisterLinkResponse\"\x00\x12_\n\nGetMetrics\x12&.milvus.proto.milvus.GetMetricsRequest\x1a\'.milvus.proto.milvus.GetMetricsResponse\"\x00\x12l\n\x12GetComponentStates\x12..milvus.proto.milvus.GetComponentStatesRequest\x1a$.milvus.proto.milvus.ComponentStates\"\x00\x12U\n\x0bLoadBalance\x12\'.milvus.proto.milvus.LoadBalanceRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12w\n\x12GetCompactionState\x12..milvus.proto.milvus.GetCompactionStateRequest\x1a/.milvus.proto.milvus.GetCompactionStateResponse\"\x00\x12q\n\x10ManualCompaction\x12,.milvus.proto.milvus.ManualCompactionRequest\x1a-.milvus.proto.milvus.ManualCompactionResponse\"\x00\x12\x80\x01\n\x1bGetCompactionStateWithPlans\x12..milvus.proto.milvus.GetCompactionPlansRequest\x1a/.milvus.proto.milvus.GetCompactionPlansResponse\"\x00\x12S\n\x06Import\x12\".milvus.proto.milvus.ImportRequest\x1a#.milvus.proto.milvus.ImportResponse\"\x00\x12k\n\x0eGetImportState\x12*.milvus.proto.milvus.GetImportStateRequest\x1a+.milvus.proto.milvus.GetImportStateResponse\"\x00\x12n\n\x0fListImportTasks\x12+.milvus.proto.milvus.ListImportTasksRequest\x1a,.milvus.proto.milvus.ListImportTasksResponse\"\x00\x12_\n\x10\x43reateCredential\x12,.milvus.proto.milvus.CreateCredentialRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12_\n\x10UpdateCredential\x12,.milvus.proto.milvus.UpdateCredentialRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12_\n\x10\x44\x65leteCredential\x12,.milvus.proto.milvus.DeleteCredentialRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12h\n\rListCredUsers\x12).milvus.proto.milvus.ListCredUsersRequest\x1a*.milvus.proto.milvus.ListCredUsersResponse\"\x00\x12S\n\nCreateRole\x12&.milvus.proto.milvus.CreateRoleRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12O\n\x08\x44ropRole\x12$.milvus.proto.milvus.DropRoleRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12]\n\x0fOperateUserRole\x12+.milvus.proto.milvus.OperateUserRoleRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12_\n\nSelectRole\x12&.milvus.proto.milvus.SelectRoleRequest\x1a\'.milvus.proto.milvus.SelectRoleResponse\"\x00\x12_\n\nSelectUser\x12&.milvus.proto.milvus.SelectUserRequest\x1a\'.milvus.proto.milvus.SelectUserResponse\"\x00\x12_\n\x10OperatePrivilege\x12,.milvus.proto.milvus.OperatePrivilegeRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12\x63\n\x12OperatePrivilegeV2\x12..milvus.proto.milvus.OperatePrivilegeV2Request\x1a\x1b.milvus.proto.common.Status\"\x00\x12\x62\n\x0bSelectGrant\x12\'.milvus.proto.milvus.SelectGrantRequest\x1a(.milvus.proto.milvus.SelectGrantResponse\"\x00\x12_\n\nGetVersion\x12&.milvus.proto.milvus.GetVersionRequest\x1a\'.milvus.proto.milvus.GetVersionResponse\"\x00\x12\x62\n\x0b\x43heckHealth\x12\'.milvus.proto.milvus.CheckHealthRequest\x1a(.milvus.proto.milvus.CheckHealthResponse\"\x00\x12\x65\n\x13\x43reateResourceGroup\x12/.milvus.proto.milvus.CreateResourceGroupRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12\x61\n\x11\x44ropResourceGroup\x12-.milvus.proto.milvus.DropResourceGroupRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12g\n\x14UpdateResourceGroups\x12\x30.milvus.proto.milvus.UpdateResourceGroupsRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12W\n\x0cTransferNode\x12(.milvus.proto.milvus.TransferNodeRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12]\n\x0fTransferReplica\x12+.milvus.proto.milvus.TransferReplicaRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12w\n\x12ListResourceGroups\x12..milvus.proto.milvus.ListResourceGroupsRequest\x1a/.milvus.proto.milvus.ListResourceGroupsResponse\"\x00\x12\x80\x01\n\x15\x44\x65scribeResourceGroup\x12\x31.milvus.proto.milvus.DescribeResourceGroupRequest\x1a\x32.milvus.proto.milvus.DescribeResourceGroupResponse\"\x00\x12_\n\x10RenameCollection\x12,.milvus.proto.milvus.RenameCollectionRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12u\n\x12ListIndexedSegment\x12-.milvus.proto.feder.ListIndexedSegmentRequest\x1a..milvus.proto.feder.ListIndexedSegmentResponse\"\x00\x12\x87\x01\n\x18\x44\x65scribeSegmentIndexData\x12\x33.milvus.proto.feder.DescribeSegmentIndexDataRequest\x1a\x34.milvus.proto.feder.DescribeSegmentIndexDataResponse\"\x00\x12V\n\x07\x43onnect\x12#.milvus.proto.milvus.ConnectRequest\x1a$.milvus.proto.milvus.ConnectResponse\"\x00\x12k\n\x0e\x41llocTimestamp\x12*.milvus.proto.milvus.AllocTimestampRequest\x1a+.milvus.proto.milvus.AllocTimestampResponse\"\x00\x12[\n\x0e\x43reateDatabase\x12*.milvus.proto.milvus.CreateDatabaseRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12W\n\x0c\x44ropDatabase\x12(.milvus.proto.milvus.DropDatabaseRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12h\n\rListDatabases\x12).milvus.proto.milvus.ListDatabasesRequest\x1a*.milvus.proto.milvus.ListDatabasesResponse\"\x00\x12Y\n\rAlterDatabase\x12).milvus.proto.milvus.AlterDatabaseRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12q\n\x10\x44\x65scribeDatabase\x12,.milvus.proto.milvus.DescribeDatabaseRequest\x1a-.milvus.proto.milvus.DescribeDatabaseResponse\"\x00\x12q\n\x10ReplicateMessage\x12,.milvus.proto.milvus.ReplicateMessageRequest\x1a-.milvus.proto.milvus.ReplicateMessageResponse\"\x00\x12g\n\nBackupRBAC\x12*.milvus.proto.milvus.BackupRBACMetaRequest\x1a+.milvus.proto.milvus.BackupRBACMetaResponse\"\x00\x12Y\n\x0bRestoreRBAC\x12+.milvus.proto.milvus.RestoreRBACMetaRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12g\n\x14\x43reatePrivilegeGroup\x12\x30.milvus.proto.milvus.CreatePrivilegeGroupRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12\x63\n\x12\x44ropPrivilegeGroup\x12..milvus.proto.milvus.DropPrivilegeGroupRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12z\n\x13ListPrivilegeGroups\x12/.milvus.proto.milvus.ListPrivilegeGroupsRequest\x1a\x30.milvus.proto.milvus.ListPrivilegeGroupsResponse\"\x00\x12i\n\x15OperatePrivilegeGroup\x12\x31.milvus.proto.milvus.OperatePrivilegeGroupRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12\x62\n\x0bRunAnalyzer\x12\'.milvus.proto.milvus.RunAnalyzerRequest\x1a(.milvus.proto.milvus.RunAnalyzerResponse\"\x00\x12U\n\x0b\x41\x64\x64UserTags\x12\'.milvus.proto.milvus.AddUserTagsRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12[\n\x0e\x44\x65leteUserTags\x12*.milvus.proto.milvus.DeleteUserTagsRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12\x62\n\x0bGetUserTags\x12\'.milvus.proto.milvus.GetUserTagsRequest\x1a(.milvus.proto.milvus.GetUserTagsResponse\"\x00\x12q\n\x10ListUsersWithTag\x12,.milvus.proto.milvus.ListUsersWithTagRequest\x1a-.milvus.proto.milvus.ListUsersWithTagResponse\"\x00\x12]\n\x0f\x43reateRowPolicy\x12+.milvus.proto.milvus.CreateRowPolicyRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12Y\n\rDropRowPolicy\x12).milvus.proto.milvus.DropRowPolicyRequest\x1a\x1b.milvus.proto.common.Status\"\x00\x12n\n\x0fListRowPolicies\x12+.milvus.proto.milvus.ListRowPoliciesRequest\x1a,.milvus.proto.milvus.ListRowPoliciesResponse\"\x00\x32u\n\x0cProxyService\x12\x65\n\x0cRegisterLink\x12(.milvus.proto.milvus.RegisterLinkRequest\x1a).milvus.proto.milvus.RegisterLinkResponse\"\x00:U\n\x0emilvus_ext_obj\x12\x1c.google.protobuf.FileOptions\x18\xe9\x07 \x01(\x0b\x32\x1e.milvus.proto.milvus.MilvusExtBm\n\x0eio.milvus.grpcB\x0bMilvusProtoP\x01Z4github.com/milvus-io/milvus-proto/go-api/v2/milvuspb\xa0\x01\x01\xaa\x02\x12Milvus.Client.Grpcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'milvus_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\016io.milvus.grpcB\013MilvusProtoP\001Z4github.com/milvus-io/milvus-proto/go-api/v2/milvuspb\240\001\001\252\002\022Milvus.Client.Grpc'
  _globals['_SHOWTYPE']._loaded_options = None
  _globals['_SHOWTYPE']._serialized_options = b'\030\001'
  _globals['_CREATEALIASREQUEST']._loaded_options = None
  _globals['_CREATEALIASREQUEST']._serialized_options = b'\312>\017\010\001\020,\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DROPALIASREQUEST']._loaded_options = None
  _globals['_DROPALIASREQUEST']._serialized_options = b'\312>\017\010\001\020-\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_ALTERALIASREQUEST']._loaded_options = None
  _globals['_ALTERALIASREQUEST']._serialized_options = b'\312>\017\010\001\020,\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DESCRIBEALIASREQUEST']._loaded_options = None
  _globals['_DESCRIBEALIASREQUEST']._serialized_options = b'\312>\017\010\001\020.\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_LISTALIASESREQUEST']._loaded_options = None
  _globals['_LISTALIASESREQUEST']._serialized_options = b'\312>\017\010\001\020/\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_CREATECOLLECTIONREQUEST']._loaded_options = None
  _globals['_CREATECOLLECTIONREQUEST']._serialized_options = b'\312>\017\010\001\020\001\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DROPCOLLECTIONREQUEST']._loaded_options = None
  _globals['_DROPCOLLECTIONREQUEST']._serialized_options = b'\312>\017\010\001\020\002\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_ALTERCOLLECTIONREQUEST']._loaded_options = None
  _globals['_ALTERCOLLECTIONREQUEST']._serialized_options = b'\312>\017\010\001\020\001\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_ALTERCOLLECTIONFIELDREQUEST']._loaded_options = None
  _globals['_ALTERCOLLECTIONFIELDREQUEST']._serialized_options = b'\312>\017\010\001\020\001\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DESCRIBECOLLECTIONREQUEST']._loaded_options = None
  _globals['_DESCRIBECOLLECTIONREQUEST']._serialized_options = b'\312>\017\010\001\020\003\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_LOADCOLLECTIONREQUEST_LOADPARAMSENTRY']._loaded_options = None
  _globals['_LOADCOLLECTIONREQUEST_LOADPARAMSENTRY']._serialized_options = b'8\001'
  _globals['_LOADCOLLECTIONREQUEST']._loaded_options = None
  _globals['_LOADCOLLECTIONREQUEST']._serialized_options = b'\312>\004\020\005\030\003'
  _globals['_RELEASECOLLECTIONREQUEST']._loaded_options = None
  _globals['_RELEASECOLLECTIONREQUEST']._serialized_options = b'\312>\004\020\006\030\003'
  _globals['_GETSTATISTICSREQUEST']._loaded_options = None
  _globals['_GETSTATISTICSREQUEST']._serialized_options = b'\312>\004\020\n\030\003'
  _globals['_GETCOLLECTIONSTATISTICSREQUEST']._loaded_options = None
  _globals['_GETCOLLECTIONSTATISTICSREQUEST']._serialized_options = b'\312>\004\020\n\030\003'
  _globals['_SHOWCOLLECTIONSREQUEST'].fields_by_name['collection_names']._loaded_options = None
  _globals['_SHOWCOLLECTIONSREQUEST'].fields_by_name['collection_names']._serialized_options = b'\030\001'
  _globals['_SHOWCOLLECTIONSRESPONSE'].fields_by_name['inMemory_percentages']._loaded_options = None
  _globals['_SHOWCOLLECTIONSRESPONSE'].fields_by_name['inMemory_percentages']._serialized_options = b'\030\001'
  _globals['_CREATEPARTITIONREQUEST']._loaded_options = None
  _globals['_CREATEPARTITIONREQUEST']._serialized_options = b'\312>\004\020\'\030\003'
  _globals['_DROPPARTITIONREQUEST']._loaded_options = None
  _globals['_DROPPARTITIONREQUEST']._serialized_options = b'\312>\004\020(\030\003'
  _globals['_HASPARTITIONREQUEST']._loaded_options = None
  _globals['_HASPARTITIONREQUEST']._serialized_options = b'\312>\004\020*\030\003'
  _globals['_LOADPARTITIONSREQUEST_LOADPARAMSENTRY']._loaded_options = None
  _globals['_LOADPARTITIONSREQUEST_LOADPARAMSENTRY']._serialized_options = b'8\001'
  _globals['_LOADPARTITIONSREQUEST']._loaded_options = None
  _globals['_LOADPARTITIONSREQUEST']._serialized_options = b'\312>\004\020\005\030\003'
  _globals['_RELEASEPARTITIONSREQUEST']._loaded_options = None
  _globals['_RELEASEPARTITIONSREQUEST']._serialized_options = b'\312>\004\020\006\030\003'
  _globals['_SHOWPARTITIONSREQUEST'].fields_by_name['type']._loaded_options = None
  _globals['_SHOWPARTITIONSREQUEST'].fields_by_name['type']._serialized_options = b'\030\001'
  _globals['_SHOWPARTITIONSREQUEST']._loaded_options = None
  _globals['_SHOWPARTITIONSREQUEST']._serialized_options = b'\312>\004\020)\030\003'
  _globals['_SHOWPARTITIONSRESPONSE'].fields_by_name['inMemory_percentages']._loaded_options = None
  _globals['_SHOWPARTITIONSRESPONSE'].fields_by_name['inMemory_percentages']._serialized_options = b'\030\001'
  _globals['_CREATEINDEXREQUEST']._loaded_options = None
  _globals['_CREATEINDEXREQUEST']._serialized_options = b'\312>\004\020\013\030\003'
  _globals['_ALTERINDEXREQUEST']._loaded_options = None
  _globals['_ALTERINDEXREQUEST']._serialized_options = b'\312>\004\020\013\030\003'
  _globals['_DESCRIBEINDEXREQUEST']._loaded_options = None
  _globals['_DESCRIBEINDEXREQUEST']._serialized_options = b'\312>\004\020\014\030\003'
  _globals['_GETINDEXBUILDPROGRESSREQUEST']._loaded_options = None
  _globals['_GETINDEXBUILDPROGRESSREQUEST']._serialized_options = b'\312>\004\020\014\030\003'
  _globals['_GETINDEXSTATEREQUEST']._loaded_options = None
  _globals['_GETINDEXSTATEREQUEST']._serialized_options = b'\312>\004\020\014\030\003'
  _globals['_DROPINDEXREQUEST']._loaded_options = None
  _globals['_DROPINDEXREQUEST']._serialized_options = b'\312>\004\020\r\030\003'
  _globals['_INSERTREQUEST']._loaded_options = None
  _globals['_INSERTREQUEST']._serialized_options = b'\312>\004\020\010\030\003'
  _globals['_ADDCOLLECTIONFIELDREQUEST']._loaded_options = None
  _globals['_ADDCOLLECTIONFIELDREQUEST']._serialized_options = b'\312>\004\020G\030\003'
  _globals['_UPSERTREQUEST']._loaded_options = None
  _globals['_UPSERTREQUEST']._serialized_options = b'\312>\004\020\031\030\003'
  _globals['_DELETEREQUEST_EXPRTEMPLATEVALUESENTRY']._loaded_options = None
  _globals['_DELETEREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_options = b'8\001'
  _globals['_DELETEREQUEST']._loaded_options = None
  _globals['_DELETEREQUEST']._serialized_options = b'\312>\004\020\t\030\003'
  _globals['_SUBSEARCHREQUEST_EXPRTEMPLATEVALUESENTRY']._loaded_options = None
  _globals['_SUBSEARCHREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_options = b'8\001'
  _globals['_SEARCHREQUEST_EXPRTEMPLATEVALUESENTRY']._loaded_options = None
  _globals['_SEARCHREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_options = b'8\001'
  _globals['_SEARCHREQUEST']._loaded_options = None
  _globals['_SEARCHREQUEST']._serialized_options = b'\312>\004\020\016\030\003'
  _globals['_HYBRIDSEARCHREQUEST']._loaded_options = None
  _globals['_HYBRIDSEARCHREQUEST']._serialized_options = b'\312>\004\020\016\030\003'
  _globals['_FLUSHREQUEST']._loaded_options = None
  _globals['_FLUSHREQUEST']._serialized_options = b'\312>\004\020\017 \003'
  _globals['_FLUSHRESPONSE_COLLSEGIDSENTRY']._loaded_options = None
  _globals['_FLUSHRESPONSE_COLLSEGIDSENTRY']._serialized_options = b'8\001'
  _globals['_FLUSHRESPONSE_FLUSHCOLLSEGIDSENTRY']._loaded_options = None
  _globals['_FLUSHRESPONSE_FLUSHCOLLSEGIDSENTRY']._serialized_options = b'8\001'
  _globals['_FLUSHRESPONSE_COLLSEALTIMESENTRY']._loaded_options = None
  _globals['_FLUSHRESPONSE_COLLSEALTIMESENTRY']._serialized_options = b'8\001'
  _globals['_FLUSHRESPONSE_COLLFLUSHTSENTRY']._loaded_options = None
  _globals['_FLUSHRESPONSE_COLLFLUSHTSENTRY']._serialized_options = b'8\001'
  _globals['_FLUSHRESPONSE_CHANNELCPSENTRY']._loaded_options = None
  _globals['_FLUSHRESPONSE_CHANNELCPSENTRY']._serialized_options = b'8\001'
  _globals['_QUERYREQUEST_EXPRTEMPLATEVALUESENTRY']._loaded_options = None
  _globals['_QUERYREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_options = b'8\001'
  _globals['_QUERYREQUEST']._loaded_options = None
  _globals['_QUERYREQUEST']._serialized_options = b'\312>\004\020\020\030\003'
  _globals['_FLUSHALLREQUEST']._loaded_options = None
  _globals['_FLUSHALLREQUEST']._serialized_options = b'\312>\017\010\001\020&\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_QUERYSEGMENTINFO'].fields_by_name['nodeID']._loaded_options = None
  _globals['_QUERYSEGMENTINFO'].fields_by_name['nodeID']._serialized_options = b'\030\001'
  _globals['_LOADBALANCEREQUEST']._loaded_options = None
  _globals['_LOADBALANCEREQUEST']._serialized_options = b'\312>\004\020\021\030\005'
  _globals['_MANUALCOMPACTIONREQUEST']._loaded_options = None
  _globals['_MANUALCOMPACTIONREQUEST']._serialized_options = b'\312>\004\020\007\030\004'
  _globals['_GETFLUSHSTATEREQUEST']._loaded_options = None
  _globals['_GETFLUSHSTATEREQUEST']._serialized_options = b'\312>\004\020+\030\004'
  _globals['_IMPORTREQUEST']._loaded_options = None
  _globals['_IMPORTREQUEST']._serialized_options = b'\312>\004\020\022\030\001'
  _globals['_REPLICAINFO_NUMOUTBOUNDNODEENTRY']._loaded_options = None
  _globals['_REPLICAINFO_NUMOUTBOUNDNODEENTRY']._serialized_options = b'8\001'
  _globals['_CREATECREDENTIALREQUEST']._loaded_options = None
  _globals['_CREATECREDENTIALREQUEST']._serialized_options = b'\312>\017\010\001\020\023\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_UPDATECREDENTIALREQUEST']._loaded_options = None
  _globals['_UPDATECREDENTIALREQUEST']._serialized_options = b'\312>\006\010\002\020\024\030\002'
  _globals['_DELETECREDENTIALREQUEST']._loaded_options = None
  _globals['_DELETECREDENTIALREQUEST']._serialized_options = b'\312>\017\010\001\020\025\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_LISTCREDUSERSREQUEST']._loaded_options = None
  _globals['_LISTCREDUSERSREQUEST']._serialized_options = b'\312>\017\010\001\020\026\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_CREATEROLEREQUEST']._loaded_options = None
  _globals['_CREATEROLEREQUEST']._serialized_options = b'\312>\017\010\001\020\023\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DROPROLEREQUEST']._loaded_options = None
  _globals['_DROPROLEREQUEST']._serialized_options = b'\312>\017\010\001\020\025\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_CREATEPRIVILEGEGROUPREQUEST']._loaded_options = None
  _globals['_CREATEPRIVILEGEGROUPREQUEST']._serialized_options = b'\312>\017\010\001\0208\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DROPPRIVILEGEGROUPREQUEST']._loaded_options = None
  _globals['_DROPPRIVILEGEGROUPREQUEST']._serialized_options = b'\312>\017\010\001\0209\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_LISTPRIVILEGEGROUPSREQUEST']._loaded_options = None
  _globals['_LISTPRIVILEGEGROUPSREQUEST']._serialized_options = b'\312>\017\010\001\020:\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_OPERATEPRIVILEGEGROUPREQUEST']._loaded_options = None
  _globals['_OPERATEPRIVILEGEGROUPREQUEST']._serialized_options = b'\312>\017\010\001\020;\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_OPERATEUSERROLEREQUEST']._loaded_options = None
  _globals['_OPERATEUSERROLEREQUEST']._serialized_options = b'\312>\017\010\001\020\027\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_SELECTROLEREQUEST']._loaded_options = None
  _globals['_SELECTROLEREQUEST']._serialized_options = b'\312>\017\010\001\020\026\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_SELECTUSERREQUEST']._loaded_options = None
  _globals['_SELECTUSERREQUEST']._serialized_options = b'\312>\006\010\002\020\030\030\002'
  _globals['_SELECTGRANTREQUEST']._loaded_options = None
  _globals['_SELECTGRANTREQUEST']._serialized_options = b'\312>\017\010\001\020\026\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_OPERATEPRIVILEGEREQUEST']._loaded_options = None
  _globals['_OPERATEPRIVILEGEREQUEST']._serialized_options = b'\312>\017\010\001\020\027\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_OPERATEPRIVILEGEV2REQUEST']._loaded_options = None
  _globals['_OPERATEPRIVILEGEV2REQUEST']._serialized_options = b'\312>\017\010\001\020\027\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_BACKUPRBACMETAREQUEST']._loaded_options = None
  _globals['_BACKUPRBACMETAREQUEST']._serialized_options = b'\312>\017\010\001\0203\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_RESTORERBACMETAREQUEST']._loaded_options = None
  _globals['_RESTORERBACMETAREQUEST']._serialized_options = b'\312>\017\010\001\0204\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_GETLOADINGPROGRESSREQUEST']._loaded_options = None
  _globals['_GETLOADINGPROGRESSREQUEST']._serialized_options = b'\312>\004\020!\030\002'
  _globals['_GETLOADSTATEREQUEST']._loaded_options = None
  _globals['_GETLOADSTATEREQUEST']._serialized_options = b'\312>\004\020!\030\002'
  _globals['_CREATERESOURCEGROUPREQUEST']._loaded_options = None
  _globals['_CREATERESOURCEGROUPREQUEST']._serialized_options = b'\312>\017\010\001\020\032\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_UPDATERESOURCEGROUPSREQUEST_RESOURCEGROUPSENTRY']._loaded_options = None
  _globals['_UPDATERESOURCEGROUPSREQUEST_RESOURCEGROUPSENTRY']._serialized_options = b'8\001'
  _globals['_UPDATERESOURCEGROUPSREQUEST']._loaded_options = None
  _globals['_UPDATERESOURCEGROUPSREQUEST']._serialized_options = b'\312>\017\010\001\0200\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DROPRESOURCEGROUPREQUEST']._loaded_options = None
  _globals['_DROPRESOURCEGROUPREQUEST']._serialized_options = b'\312>\017\010\001\020\033\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_TRANSFERNODEREQUEST']._loaded_options = None
  _globals['_TRANSFERNODEREQUEST']._serialized_options = b'\312>\017\010\001\020\036\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_TRANSFERREPLICAREQUEST']._loaded_options = None
  _globals['_TRANSFERREPLICAREQUEST']._serialized_options = b'\312>\017\010\001\020\037\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_LISTRESOURCEGROUPSREQUEST']._loaded_options = None
  _globals['_LISTRESOURCEGROUPSREQUEST']._serialized_options = b'\312>\017\010\001\020\035\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DESCRIBERESOURCEGROUPREQUEST']._loaded_options = None
  _globals['_DESCRIBERESOURCEGROUPREQUEST']._serialized_options = b'\312>\017\010\001\020\034\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_RESOURCEGROUP_NUMLOADEDREPLICAENTRY']._loaded_options = None
  _globals['_RESOURCEGROUP_NUMLOADEDREPLICAENTRY']._serialized_options = b'8\001'
  _globals['_RESOURCEGROUP_NUMOUTGOINGNODEENTRY']._loaded_options = None
  _globals['_RESOURCEGROUP_NUMOUTGOINGNODEENTRY']._serialized_options = b'8\001'
  _globals['_RESOURCEGROUP_NUMINCOMINGNODEENTRY']._loaded_options = None
  _globals['_RESOURCEGROUP_NUMINCOMINGNODEENTRY']._serialized_options = b'8\001'
  _globals['_RENAMECOLLECTIONREQUEST']._loaded_options = None
  _globals['_RENAMECOLLECTIONREQUEST']._serialized_options = b'\312>\017\010\001\020\"\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_GETINDEXSTATISTICSREQUEST']._loaded_options = None
  _globals['_GETINDEXSTATISTICSREQUEST']._serialized_options = b'\312>\004\020\014\030\003'
  _globals['_CREATEDATABASEREQUEST']._loaded_options = None
  _globals['_CREATEDATABASEREQUEST']._serialized_options = b'\312>\017\010\001\020#\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DROPDATABASEREQUEST']._loaded_options = None
  _globals['_DROPDATABASEREQUEST']._serialized_options = b'\312>\017\010\001\020$\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_ALTERDATABASEREQUEST']._loaded_options = None
  _globals['_ALTERDATABASEREQUEST']._serialized_options = b'\312>\017\010\001\0201\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_DESCRIBEDATABASEREQUEST']._loaded_options = None
  _globals['_DESCRIBEDATABASEREQUEST']._serialized_options = b'\312>\017\010\001\0202\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_IMPORTAUTHPLACEHOLDER']._loaded_options = None
  _globals['_IMPORTAUTHPLACEHOLDER']._serialized_options = b'\312>\004\020\022\030\002'
  _globals['_GETIMPORTPROGRESSAUTHPLACEHOLDER']._loaded_options = None
  _globals['_GETIMPORTPROGRESSAUTHPLACEHOLDER']._serialized_options = b'\312>\017\010\001\020E\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_LISTIMPORTSAUTHPLACEHOLDER']._loaded_options = None
  _globals['_LISTIMPORTSAUTHPLACEHOLDER']._serialized_options = b'\312>\017\010\001\020F\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_ADDUSERTAGSREQUEST_TAGSENTRY']._loaded_options = None
  _globals['_ADDUSERTAGSREQUEST_TAGSENTRY']._serialized_options = b'8\001'
  _globals['_ADDUSERTAGSREQUEST']._loaded_options = None
  _globals['_ADDUSERTAGSREQUEST']._serialized_options = b'\312>\006\010\002\020\024\030\002'
  _globals['_DELETEUSERTAGSREQUEST']._loaded_options = None
  _globals['_DELETEUSERTAGSREQUEST']._serialized_options = b'\312>\006\010\002\020\024\030\002'
  _globals['_GETUSERTAGSREQUEST']._loaded_options = None
  _globals['_GETUSERTAGSREQUEST']._serialized_options = b'\312>\006\010\002\020\030\030\002'
  _globals['_GETUSERTAGSRESPONSE_TAGSENTRY']._loaded_options = None
  _globals['_GETUSERTAGSRESPONSE_TAGSENTRY']._serialized_options = b'8\001'
  _globals['_LISTUSERSWITHTAGREQUEST']._loaded_options = None
  _globals['_LISTUSERSWITHTAGREQUEST']._serialized_options = b'\312>\017\010\002\020\030\030\377\377\377\377\377\377\377\377\377\001'
  _globals['_CREATEROWPOLICYREQUEST']._loaded_options = None
  _globals['_CREATEROWPOLICYREQUEST']._serialized_options = b'\312>\004\020\023\030\003'
  _globals['_DROPROWPOLICYREQUEST']._loaded_options = None
  _globals['_DROPROWPOLICYREQUEST']._serialized_options = b'\312>\004\020\025\030\003'
  _globals['_LISTROWPOLICIESREQUEST']._loaded_options = None
  _globals['_LISTROWPOLICIESREQUEST']._serialized_options = b'\312>\004\020\026\030\003'
  _globals['_MILVUSSERVICE'].methods_by_name['GetIndexState']._loaded_options = None
  _globals['_MILVUSSERVICE'].methods_by_name['GetIndexState']._serialized_options = b'\210\002\001'
  _globals['_MILVUSSERVICE'].methods_by_name['GetIndexBuildProgress']._loaded_options = None
  _globals['_MILVUSSERVICE'].methods_by_name['GetIndexBuildProgress']._serialized_options = b'\210\002\001'
  _globals['_SHOWTYPE']._serialized_start=31576
  _globals['_SHOWTYPE']._serialized_end=31613
  _globals['_OPERATEPRIVILEGEGROUPTYPE']._serialized_start=31615
  _globals['_OPERATEPRIVILEGEGROUPTYPE']._serialized_end=31699
  _globals['_OPERATEUSERROLETYPE']._serialized_start=31701
  _globals['_OPERATEUSERROLETYPE']._serialized_end=31765
  _globals['_PRIVILEGELEVEL']._serialized_start=31767
  _globals['_PRIVILEGELEVEL']._serialized_end=31826
  _globals['_OPERATEPRIVILEGETYPE']._serialized_start=31828
  _globals['_OPERATEPRIVILEGETYPE']._serialized_end=31873
  _globals['_QUOTASTATE']._serialized_start=31875
  _globals['_QUOTASTATE']._serialized_end=31983
  _globals['_ROWPOLICYACTION']._serialized_start=31985
  _globals['_ROWPOLICYACTION']._serialized_end=32061
  _globals['_CREATEALIASREQUEST']._serialized_start=134
  _globals['_CREATEALIASREQUEST']._serialized_end=275
  _globals['_DROPALIASREQUEST']._serialized_start=277
  _globals['_DROPALIASREQUEST']._serialized_end=391
  _globals['_ALTERALIASREQUEST']._serialized_start=394
  _globals['_ALTERALIASREQUEST']._serialized_end=534
  _globals['_DESCRIBEALIASREQUEST']._serialized_start=536
  _globals['_DESCRIBEALIASREQUEST']._serialized_end=654
  _globals['_DESCRIBEALIASRESPONSE']._serialized_start=656
  _globals['_DESCRIBEALIASRESPONSE']._serialized_end=776
  _globals['_LISTALIASESREQUEST']._serialized_start=778
  _globals['_LISTALIASESREQUEST']._serialized_end=904
  _globals['_LISTALIASESRESPONSE']._serialized_start=906
  _globals['_LISTALIASESRESPONSE']._serialized_end=1031
  _globals['_CREATECOLLECTIONREQUEST']._serialized_start=1034
  _globals['_CREATECOLLECTIONREQUEST']._serialized_end=1346
  _globals['_DROPCOLLECTIONREQUEST']._serialized_start=1349
  _globals['_DROPCOLLECTIONREQUEST']._serialized_end=1478
  _globals['_ALTERCOLLECTIONREQUEST']._serialized_start=1481
  _globals['_ALTERCOLLECTIONREQUEST']._serialized_end=1709
  _globals['_ALTERCOLLECTIONFIELDREQUEST']._serialized_start=1712
  _globals['_ALTERCOLLECTIONFIELDREQUEST']._serialized_end=1943
  _globals['_HASCOLLECTIONREQUEST']._serialized_start=1946
  _globals['_HASCOLLECTIONREQUEST']._serialized_end=2074
  _globals['_BOOLRESPONSE']._serialized_start=2076
  _globals['_BOOLRESPONSE']._serialized_end=2150
  _globals['_STRINGRESPONSE']._serialized_start=2152
  _globals['_STRINGRESPONSE']._serialized_end=2228
  _globals['_DESCRIBECOLLECTIONREQUEST']._serialized_start=2231
  _globals['_DESCRIBECOLLECTIONREQUEST']._serialized_end=2406
  _globals['_DESCRIBECOLLECTIONRESPONSE']._serialized_start=2409
  _globals['_DESCRIBECOLLECTIONRESPONSE']._serialized_end=3056
  _globals['_LOADCOLLECTIONREQUEST']._serialized_start=3059
  _globals['_LOADCOLLECTIONREQUEST']._serialized_end=3429
  _globals['_LOADCOLLECTIONREQUEST_LOADPARAMSENTRY']._serialized_start=3371
  _globals['_LOADCOLLECTIONREQUEST_LOADPARAMSENTRY']._serialized_end=3420
  _globals['_RELEASECOLLECTIONREQUEST']._serialized_start=3431
  _globals['_RELEASECOLLECTIONREQUEST']._serialized_end=3552
  _globals['_GETSTATISTICSREQUEST']._serialized_start=3555
  _globals['_GETSTATISTICSREQUEST']._serialized_end=3726
  _globals['_GETSTATISTICSRESPONSE']._serialized_start=3728
  _globals['_GETSTATISTICSRESPONSE']._serialized_end=3846
  _globals['_GETCOLLECTIONSTATISTICSREQUEST']._serialized_start=3848
  _globals['_GETCOLLECTIONSTATISTICSREQUEST']._serialized_end=3975
  _globals['_GETCOLLECTIONSTATISTICSRESPONSE']._serialized_start=3978
  _globals['_GETCOLLECTIONSTATISTICSRESPONSE']._serialized_end=4106
  _globals['_SHOWCOLLECTIONSREQUEST']._serialized_start=4109
  _globals['_SHOWCOLLECTIONSREQUEST']._serialized_end=4289
  _globals['_SHOWCOLLECTIONSRESPONSE']._serialized_start=4292
  _globals['_SHOWCOLLECTIONSRESPONSE']._serialized_end=4539
  _globals['_CREATEPARTITIONREQUEST']._serialized_start=4542
  _globals['_CREATEPARTITIONREQUEST']._serialized_end=4685
  _globals['_DROPPARTITIONREQUEST']._serialized_start=4688
  _globals['_DROPPARTITIONREQUEST']._serialized_end=4829
  _globals['_HASPARTITIONREQUEST']._serialized_start=4832
  _globals['_HASPARTITIONREQUEST']._serialized_end=4972
  _globals['_LOADPARTITIONSREQUEST']._serialized_start=4975
  _globals['_LOADPARTITIONSREQUEST']._serialized_end=5370
  _globals['_LOADPARTITIONSREQUEST_LOADPARAMSENTRY']._serialized_start=3371
  _globals['_LOADPARTITIONSREQUEST_LOADPARAMSENTRY']._serialized_end=3420
  _globals['_RELEASEPARTITIONSREQUEST']._serialized_start=5373
  _globals['_RELEASEPARTITIONSREQUEST']._serialized_end=5519
  _globals['_GETPARTITIONSTATISTICSREQUEST']._serialized_start=5522
  _globals['_GETPARTITIONSTATISTICSREQUEST']._serialized_end=5663
  _globals['_GETPARTITIONSTATISTICSRESPONSE']._serialized_start=5665
  _globals['_GETPARTITIONSTATISTICSRESPONSE']._serialized_end=5792
  _globals['_SHOWPARTITIONSREQUEST']._serialized_start=5795
  _globals['_SHOWPARTITIONSREQUEST']._serialized_end=6009
  _globals['_SHOWPARTITIONSRESPONSE']._serialized_start=6012
  _globals['_SHOWPARTITIONSRESPONSE']._serialized_end=6222
  _globals['_DESCRIBESEGMENTREQUEST']._serialized_start=6224
  _globals['_DESCRIBESEGMENTREQUEST']._serialized_end=6333
  _globals['_DESCRIBESEGMENTRESPONSE']._serialized_start=6336
  _globals['_DESCRIBESEGMENTRESPONSE']._serialized_end=6479
  _globals['_SHOWSEGMENTSREQUEST']._serialized_start=6481
  _globals['_SHOWSEGMENTSREQUEST']._serialized_end=6589
  _globals['_SHOWSEGMENTSRESPONSE']._serialized_start=6591
  _globals['_SHOWSEGMENTSRESPONSE']._serialized_end=6678
  _globals['_CREATEINDEXREQUEST']._serialized_start=6681
  _globals['_CREATEINDEXREQUEST']._serialized_end=6893
  _globals['_ALTERINDEXREQUEST']._serialized_start=6896
  _globals['_ALTERINDEXREQUEST']._serialized_end=7108
  _globals['_DESCRIBEINDEXREQUEST']._serialized_start=7111
  _globals['_DESCRIBEINDEXREQUEST']._serialized_end=7287
  _globals['_INDEXDESCRIPTION']._serialized_start=7290
  _globals['_INDEXDESCRIPTION']._serialized_end=7621
  _globals['_DESCRIBEINDEXRESPONSE']._serialized_start=7624
  _globals['_DESCRIBEINDEXRESPONSE']._serialized_end=7759
  _globals['_GETINDEXBUILDPROGRESSREQUEST']._serialized_start=7762
  _globals['_GETINDEXBUILDPROGRESSREQUEST']._serialized_end=7927
  _globals['_GETINDEXBUILDPROGRESSRESPONSE']._serialized_start=7929
  _globals['_GETINDEXBUILDPROGRESSRESPONSE']._serialized_end=8047
  _globals['_GETINDEXSTATEREQUEST']._serialized_start=8050
  _globals['_GETINDEXSTATEREQUEST']._serialized_end=8207
  _globals['_GETINDEXSTATERESPONSE']._serialized_start=8210
  _globals['_GETINDEXSTATERESPONSE']._serialized_end=8347
  _globals['_DROPINDEXREQUEST']._serialized_start=8350
  _globals['_DROPINDEXREQUEST']._serialized_end=8503
  _globals['_INSERTREQUEST']._serialized_start=8506
  _globals['_INSERTREQUEST']._serialized_end=8756
  _globals['_ADDCOLLECTIONFIELDREQUEST']._serialized_start=8759
  _globals['_ADDCOLLECTIONFIELDREQUEST']._serialized_end=8919
  _globals['_UPSERTREQUEST']._serialized_start=8922
  _globals['_UPSERTREQUEST']._serialized_end=9172
  _globals['_MUTATIONRESULT']._serialized_start=9175
  _globals['_MUTATIONRESULT']._serialized_end=9415
  _globals['_DELETEREQUEST']._serialized_start=9418
  _globals['_DELETEREQUEST']._serialized_end=9836
  _globals['_DELETEREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_start=9734
  _globals['_DELETEREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_end=9827
  _globals['_SUBSEARCHREQUEST']._serialized_start=9839
  _globals['_SUBSEARCHREQUEST']._serialized_end=10203
  _globals['_SUBSEARCHREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_start=9734
  _globals['_SUBSEARCHREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_end=9827
  _globals['_SEARCHREQUEST']._serialized_start=10206
  _globals['_SEARCHREQUEST']._serialized_end=11039
  _globals['_SEARCHREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_start=9734
  _globals['_SEARCHREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_end=9827
  _globals['_HITS']._serialized_start=11041
  _globals['_HITS']._serialized_end=11094
  _globals['_SEARCHRESULTS']._serialized_start=11097
  _globals['_SEARCHRESULTS']._serialized_end=11258
  _globals['_HYBRIDSEARCHREQUEST']._serialized_start=11261
  _globals['_HYBRIDSEARCHREQUEST']._serialized_end=11778
  _globals['_FLUSHREQUEST']._serialized_start=11780
  _globals['_FLUSHREQUEST']._serialized_end=11890
  _globals['_FLUSHRESPONSE']._serialized_start=11893
  _globals['_FLUSHRESPONSE']._serialized_end=12715
  _globals['_FLUSHRESPONSE_COLLSEGIDSENTRY']._serialized_start=12358
  _globals['_FLUSHRESPONSE_COLLSEGIDSENTRY']._serialized_end=12439
  _globals['_FLUSHRESPONSE_FLUSHCOLLSEGIDSENTRY']._serialized_start=12441
  _globals['_FLUSHRESPONSE_FLUSHCOLLSEGIDSENTRY']._serialized_end=12527
  _globals['_FLUSHRESPONSE_COLLSEALTIMESENTRY']._serialized_start=12529
  _globals['_FLUSHRESPONSE_COLLSEALTIMESENTRY']._serialized_end=12581
  _globals['_FLUSHRESPONSE_COLLFLUSHTSENTRY']._serialized_start=12583
  _globals['_FLUSHRESPONSE_COLLFLUSHTSENTRY']._serialized_end=12633
  _globals['_FLUSHRESPONSE_CHANNELCPSENTRY']._serialized_start=12635
  _globals['_FLUSHRESPONSE_CHANNELCPSENTRY']._serialized_end=12715
  _globals['_QUERYREQUEST']._serialized_start=12718
  _globals['_QUERYREQUEST']._serialized_end=13313
  _globals['_QUERYREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_start=9734
  _globals['_QUERYREQUEST_EXPRTEMPLATEVALUESENTRY']._serialized_end=9827
  _globals['_QUERYRESULTS']._serialized_start=13316
  _globals['_QUERYRESULTS']._serialized_end=13524
  _globals['_QUERYCURSOR']._serialized_start=13526
  _globals['_QUERYCURSOR']._serialized_end=13608
  _globals['_VECTORIDS']._serialized_start=13610
  _globals['_VECTORIDS']._serialized_end=13735
  _globals['_VECTORSARRAY']._serialized_start=13738
  _globals['_VECTORSARRAY']._serialized_end=13869
  _globals['_CALCDISTANCEREQUEST']._serialized_start=13872
  _globals['_CALCDISTANCEREQUEST']._serialized_end=14093
  _globals['_CALCDISTANCERESULTS']._serialized_start=14096
  _globals['_CALCDISTANCERESULTS']._serialized_end=14277
  _globals['_FLUSHALLREQUEST']._serialized_start=14279
  _globals['_FLUSHALLREQUEST']._serialized_end=14377
  _globals['_FLUSHALLRESPONSE']._serialized_start=14379
  _globals['_FLUSHALLRESPONSE']._serialized_end=14464
  _globals['_PERSISTENTSEGMENTINFO']._serialized_start=14467
  _globals['_PERSISTENTSEGMENTINFO']._serialized_end=14714
  _globals['_GETPERSISTENTSEGMENTINFOREQUEST']._serialized_start=14716
  _globals['_GETPERSISTENTSEGMENTINFOREQUEST']._serialized_end=14833
  _globals['_GETPERSISTENTSEGMENTINFORESPONSE']._serialized_start=14836
  _globals['_GETPERSISTENTSEGMENTINFORESPONSE']._serialized_end=14974
  _globals['_QUERYSEGMENTINFO']._serialized_start=14977
  _globals['_QUERYSEGMENTINFO']._serialized_end=15311
  _globals['_GETQUERYSEGMENTINFOREQUEST']._serialized_start=15313
  _globals['_GETQUERYSEGMENTINFOREQUEST']._serialized_end=15425
  _globals['_GETQUERYSEGMENTINFORESPONSE']._serialized_start=15428
  _globals['_GETQUERYSEGMENTINFORESPONSE']._serialized_end=15556
  _globals['_DUMMYREQUEST']._serialized_start=15558
  _globals['_DUMMYREQUEST']._serialized_end=15594
  _globals['_DUMMYRESPONSE']._serialized_start=15596
  _globals['_DUMMYRESPONSE']._serialized_end=15629
  _globals['_REGISTERLINKREQUEST']._serialized_start=15631
  _globals['_REGISTERLINKREQUEST']._serialized_end=15652
  _globals['_REGISTERLINKRESPONSE']._serialized_start=15654
  _globals['_REGISTERLINKRESPONSE']._serialized_end=15768
  _globals['_GETMETRICSREQUEST']._serialized_start=15770
  _globals['_GETMETRICSREQUEST']._serialized_end=15850
  _globals['_GETMETRICSRESPONSE']._serialized_start=15852
  _globals['_GETMETRICSRESPONSE']._serialized_end=15959
  _globals['_COMPONENTINFO']._serialized_start=15962
  _globals['_COMPONENTINFO']._serialized_end=16114
  _globals['_COMPONENTSTATES']._serialized_start=16117
  _globals['_COMPONENTSTATES']._serialized_end=16295
  _globals['_GETCOMPONENTSTATESREQUEST']._serialized_start=16297
  _globals['_GETCOMPONENTSTATESREQUEST']._serialized_end=16324
  _globals['_LOADBALANCEREQUEST']._serialized_start=16327
  _globals['_LOADBALANCEREQUEST']._serialized_end=16509
  _globals['_MANUALCOMPACTIONREQUEST']._serialized_start=16512
  _globals['_MANUALCOMPACTIONREQUEST']._serialized_end=16715
  _globals['_MANUALCOMPACTIONRESPONSE']._serialized_start=16717
  _globals['_MANUALCOMPACTIONRESPONSE']._serialized_end=16839
  _globals['_GETCOMPACTIONSTATEREQUEST']._serialized_start=16841
  _globals['_GETCOMPACTIONSTATEREQUEST']._serialized_end=16890
  _globals['_GETCOMPACTIONSTATERESPONSE']._serialized_start=16893
  _globals['_GETCOMPACTIONSTATERESPONSE']._serialized_end=17114
  _globals['_GETCOMPACTIONPLANSREQUEST']._serialized_start=17116
  _globals['_GETCOMPACTIONPLANSREQUEST']._serialized_end=17165
  _globals['_GETCOMPACTIONPLANSRESPONSE']._serialized_start=17168
  _globals['_GETCOMPACTIONPLANSRESPONSE']._serialized_end=17356
  _globals['_COMPACTIONMERGEINFO']._serialized_start=17358
  _globals['_COMPACTIONMERGEINFO']._serialized_end=17412
  _globals['_GETFLUSHSTATEREQUEST']._serialized_start=17414
  _globals['_GETFLUSHSTATEREQUEST']._serialized_end=17525
  _globals['_GETFLUSHSTATERESPONSE']._serialized_start=17527
  _globals['_GETFLUSHSTATERESPONSE']._serialized_end=17612
  _globals['_GETFLUSHALLSTATEREQUEST']._serialized_start=17614
  _globals['_GETFLUSHALLSTATEREQUEST']._serialized_end=17722
  _globals['_GETFLUSHALLSTATERESPONSE']._serialized_start=17724
  _globals['_GETFLUSHALLSTATERESPONSE']._serialized_end=17812
  _globals['_IMPORTREQUEST']._serialized_start=17815
  _globals['_IMPORTREQUEST']._serialized_end=18039
  _globals['_IMPORTRESPONSE']._serialized_start=18041
  _globals['_IMPORTRESPONSE']._serialized_end=18117
  _globals['_GETIMPORTSTATEREQUEST']._serialized_start=18119
  _globals['_GETIMPORTSTATEREQUEST']._serialized_end=18156
  _globals['_GETIMPORTSTATERESPONSE']._serialized_start=18159
  _globals['_GETIMPORTSTATERESPONSE']._serialized_end=18438
  _globals['_LISTIMPORTTASKSREQUEST']._serialized_start=18440
  _globals['_LISTIMPORTTASKSREQUEST']._serialized_end=18521
  _globals['_LISTIMPORTTASKSRESPONSE']._serialized_start=18524
  _globals['_LISTIMPORTTASKSRESPONSE']._serialized_end=18654
  _globals['_GETREPLICASREQUEST']._serialized_start=18657
  _globals['_GETREPLICASREQUEST']._serialized_end=18811
  _globals['_GETREPLICASRESPONSE']._serialized_start=18813
  _globals['_GETREPLICASRESPONSE']._serialized_end=18931
  _globals['_REPLICAINFO']._serialized_start=18934
  _globals['_REPLICAINFO']._serialized_end=19255
  _globals['_REPLICAINFO_NUMOUTBOUNDNODEENTRY']._serialized_start=19201
  _globals['_REPLICAINFO_NUMOUTBOUNDNODEENTRY']._serialized_end=19255
  _globals['_SHARDREPLICA']._serialized_start=19257
  _globals['_SHARDREPLICA']._serialized_end=19353
  _globals['_CREATECREDENTIALREQUEST']._serialized_start=19356
  _globals['_CREATECREDENTIALREQUEST']._serialized_end=19546
  _globals['_UPDATECREDENTIALREQUEST']._serialized_start=19549
  _globals['_UPDATECREDENTIALREQUEST']._serialized_end=19754
  _globals['_DELETECREDENTIALREQUEST']._serialized_start=19756
  _globals['_DELETECREDENTIALREQUEST']._serialized_end=19863
  _globals['_LISTCREDUSERSRESPONSE']._serialized_start=19865
  _globals['_LISTCREDUSERSRESPONSE']._serialized_end=19952
  _globals['_LISTCREDUSERSREQUEST']._serialized_start=19954
  _globals['_LISTCREDUSERSREQUEST']._serialized_end=20040
  _globals['_ROLEENTITY']._serialized_start=20042
  _globals['_ROLEENTITY']._serialized_end=20068
  _globals['_USERENTITY']._serialized_start=20070
  _globals['_USERENTITY']._serialized_end=20096
  _globals['_CREATEROLEREQUEST']._serialized_start=20099
  _globals['_CREATEROLEREQUEST']._serialized_end=20231
  _globals['_DROPROLEREQUEST']._serialized_start=20233
  _globals['_DROPROLEREQUEST']._serialized_end=20353
  _globals['_CREATEPRIVILEGEGROUPREQUEST']._serialized_start=20355
  _globals['_CREATEPRIVILEGEGROUPREQUEST']._serialized_end=20468
  _globals['_DROPPRIVILEGEGROUPREQUEST']._serialized_start=20470
  _globals['_DROPPRIVILEGEGROUPREQUEST']._serialized_end=20581
  _globals['_LISTPRIVILEGEGROUPSREQUEST']._serialized_start=20583
  _globals['_LISTPRIVILEGEGROUPSREQUEST']._serialized_end=20675
  _globals['_LISTPRIVILEGEGROUPSRESPONSE']._serialized_start=20678
  _globals['_LISTPRIVILEGEGROUPSRESPONSE']._serialized_end=20819
  _globals['_OPERATEPRIVILEGEGROUPREQUEST']._serialized_start=20822
  _globals['_OPERATEPRIVILEGEGROUPREQUEST']._serialized_end=21056
  _globals['_OPERATEUSERROLEREQUEST']._serialized_start=21059
  _globals['_OPERATEUSERROLEREQUEST']._serialized_end=21240
  _globals['_PRIVILEGEGROUPINFO']._serialized_start=21242
  _globals['_PRIVILEGEGROUPINFO']._serialized_end=21340
  _globals['_SELECTROLEREQUEST']._serialized_start=21343
  _globals['_SELECTROLEREQUEST']._serialized_end=21500
  _globals['_ROLERESULT']._serialized_start=21502
  _globals['_ROLERESULT']._serialized_end=21609
  _globals['_SELECTROLERESPONSE']._serialized_start=21611
  _globals['_SELECTROLERESPONSE']._serialized_end=21726
  _globals['_SELECTUSERREQUEST']._serialized_start=21729
  _globals['_SELECTUSERREQUEST']._serialized_end=21877
  _globals['_USERRESULT']._serialized_start=21879
  _globals['_USERRESULT']._serialized_end=21986
  _globals['_SELECTUSERRESPONSE']._serialized_start=21988
  _globals['_SELECTUSERRESPONSE']._serialized_end=22103
  _globals['_OBJECTENTITY']._serialized_start=22105
  _globals['_OBJECTENTITY']._serialized_end=22133
  _globals['_PRIVILEGEENTITY']._serialized_start=22135
  _globals['_PRIVILEGEENTITY']._serialized_end=22166
  _globals['_GRANTORENTITY']._serialized_start=22168
  _globals['_GRANTORENTITY']._serialized_end=22287
  _globals['_GRANTPRIVILEGEENTITY']._serialized_start=22289
  _globals['_GRANTPRIVILEGEENTITY']._serialized_end=22365
  _globals['_GRANTENTITY']._serialized_start=22368
  _globals['_GRANTENTITY']._serialized_end=22570
  _globals['_SELECTGRANTREQUEST']._serialized_start=22573
  _globals['_SELECTGRANTREQUEST']._serialized_end=22707
  _globals['_SELECTGRANTRESPONSE']._serialized_start=22709
  _globals['_SELECTGRANTRESPONSE']._serialized_end=22827
  _globals['_OPERATEPRIVILEGEREQUEST']._serialized_start=22830
  _globals['_OPERATEPRIVILEGEREQUEST']._serialized_end=23043
  _globals['_OPERATEPRIVILEGEV2REQUEST']._serialized_start=23046
  _globals['_OPERATEPRIVILEGEV2REQUEST']._serialized_end=23336
  _globals['_USERINFO']._serialized_start=23338
  _globals['_USERINFO']._serialized_end=23428
  _globals['_RBACMETA']._serialized_start=23431
  _globals['_RBACMETA']._serialized_end=23652
  _globals['_BACKUPRBACMETAREQUEST']._serialized_start=23654
  _globals['_BACKUPRBACMETAREQUEST']._serialized_end=23741
  _globals['_BACKUPRBACMETARESPONSE']._serialized_start=23743
  _globals['_BACKUPRBACMETARESPONSE']._serialized_end=23862
  _globals['_RESTORERBACMETAREQUEST']._serialized_start=23865
  _globals['_RESTORERBACMETAREQUEST']._serialized_end=24003
  _globals['_GETLOADINGPROGRESSREQUEST']._serialized_start=24006
  _globals['_GETLOADINGPROGRESSREQUEST']._serialized_end=24153
  _globals['_GETLOADINGPROGRESSRESPONSE']._serialized_start=24155
  _globals['_GETLOADINGPROGRESSRESPONSE']._serialized_end=24272
  _globals['_GETLOADSTATEREQUEST']._serialized_start=24275
  _globals['_GETLOADSTATEREQUEST']._serialized_end=24416
  _globals['_GETLOADSTATERESPONSE']._serialized_start=24418
  _globals['_GETLOADSTATERESPONSE']._serialized_end=24532
  _globals['_MILVUSEXT']._serialized_start=24534
  _globals['_MILVUSEXT']._serialized_end=24562
  _globals['_GETVERSIONREQUEST']._serialized_start=24564
  _globals['_GETVERSIONREQUEST']._serialized_end=24583
  _globals['_GETVERSIONRESPONSE']._serialized_start=24585
  _globals['_GETVERSIONRESPONSE']._serialized_end=24667
  _globals['_CHECKHEALTHREQUEST']._serialized_start=24669
  _globals['_CHECKHEALTHREQUEST']._serialized_end=24689
  _globals['_CHECKHEALTHRESPONSE']._serialized_start=24692
  _globals['_CHECKHEALTHRESPONSE']._serialized_end=24849
  _globals['_CREATERESOURCEGROUPREQUEST']._serialized_start=24852
  _globals['_CREATERESOURCEGROUPREQUEST']._serialized_end=25022
  _globals['_UPDATERESOURCEGROUPSREQUEST']._serialized_start=25025
  _globals['_UPDATERESOURCEGROUPSREQUEST']._serialized_end=25306
  _globals['_UPDATERESOURCEGROUPSREQUEST_RESOURCEGROUPSENTRY']._serialized_start=25195
  _globals['_UPDATERESOURCEGROUPSREQUEST_RESOURCEGROUPSENTRY']._serialized_end=25286
  _globals['_DROPRESOURCEGROUPREQUEST']._serialized_start=25308
  _globals['_DROPRESOURCEGROUPREQUEST']._serialized_end=25422
  _globals['_TRANSFERNODEREQUEST']._serialized_start=25425
  _globals['_TRANSFERNODEREQUEST']._serialized_end=25590
  _globals['_TRANSFERREPLICAREQUEST']._serialized_start=25593
  _globals['_TRANSFERREPLICAREQUEST']._serialized_end=25806
  _globals['_LISTRESOURCEGROUPSREQUEST']._serialized_start=25808
  _globals['_LISTRESOURCEGROUPSREQUEST']._serialized_end=25899
  _globals['_LISTRESOURCEGROUPSRESPONSE']._serialized_start=25901
  _globals['_LISTRESOURCEGROUPSRESPONSE']._serialized_end=25999
  _globals['_DESCRIBERESOURCEGROUPREQUEST']._serialized_start=26001
  _globals['_DESCRIBERESOURCEGROUPREQUEST']._serialized_end=26119
  _globals['_DESCRIBERESOURCEGROUPRESPONSE']._serialized_start=26122
  _globals['_DESCRIBERESOURCEGROUPRESPONSE']._serialized_end=26258
  _globals['_RESOURCEGROUP']._serialized_start=26261
  _globals['_RESOURCEGROUP']._serialized_end=26859
  _globals['_RESOURCEGROUP_NUMLOADEDREPLICAENTRY']._serialized_start=26692
  _globals['_RESOURCEGROUP_NUMLOADEDREPLICAENTRY']._serialized_end=26747
  _globals['_RESOURCEGROUP_NUMOUTGOINGNODEENTRY']._serialized_start=26749
  _globals['_RESOURCEGROUP_NUMOUTGOINGNODEENTRY']._serialized_end=26803
  _globals['_RESOURCEGROUP_NUMINCOMINGNODEENTRY']._serialized_start=26805
  _globals['_RESOURCEGROUP_NUMINCOMINGNODEENTRY']._serialized_end=26859
  _globals['_RENAMECOLLECTIONREQUEST']._serialized_start=26862
  _globals['_RENAMECOLLECTIONREQUEST']._serialized_end=27021
  _globals['_GETINDEXSTATISTICSREQUEST']._serialized_start=27024
  _globals['_GETINDEXSTATISTICSREQUEST']._serialized_end=27185
  _globals['_GETINDEXSTATISTICSRESPONSE']._serialized_start=27188
  _globals['_GETINDEXSTATISTICSRESPONSE']._serialized_end=27328
  _globals['_CONNECTREQUEST']._serialized_start=27330
  _globals['_CONNECTREQUEST']._serialized_end=27444
  _globals['_CONNECTRESPONSE']._serialized_start=27447
  _globals['_CONNECTRESPONSE']._serialized_end=27583
  _globals['_ALLOCTIMESTAMPREQUEST']._serialized_start=27585
  _globals['_ALLOCTIMESTAMPREQUEST']._serialized_end=27652
  _globals['_ALLOCTIMESTAMPRESPONSE']._serialized_start=27654
  _globals['_ALLOCTIMESTAMPRESPONSE']._serialized_end=27742
  _globals['_CREATEDATABASEREQUEST']._serialized_start=27745
  _globals['_CREATEDATABASEREQUEST']._serialized_end=27904
  _globals['_DROPDATABASEREQUEST']._serialized_start=27906
  _globals['_DROPDATABASEREQUEST']._serialized_end=28008
  _globals['_LISTDATABASESREQUEST']._serialized_start=28010
  _globals['_LISTDATABASESREQUEST']._serialized_end=28076
  _globals['_LISTDATABASESRESPONSE']._serialized_start=28079
  _globals['_LISTDATABASESRESPONSE']._serialized_end=28208
  _globals['_ALTERDATABASEREQUEST']._serialized_start=28211
  _globals['_ALTERDATABASEREQUEST']._serialized_end=28405
  _globals['_DESCRIBEDATABASEREQUEST']._serialized_start=28407
  _globals['_DESCRIBEDATABASEREQUEST']._serialized_end=28513
  _globals['_DESCRIBEDATABASERESPONSE']._serialized_start=28516
  _globals['_DESCRIBEDATABASERESPONSE']._serialized_end=28700
  _globals['_REPLICATEMESSAGEREQUEST']._serialized_start=28703
  _globals['_REPLICATEMESSAGEREQUEST']._serialized_end=28948
  _globals['_REPLICATEMESSAGERESPONSE']._serialized_start=28950
  _globals['_REPLICATEMESSAGERESPONSE']._serialized_end=29039
  _globals['_IMPORTAUTHPLACEHOLDER']._serialized_start=29041
  _globals['_IMPORTAUTHPLACEHOLDER']._serialized_end=29139
  _globals['_GETIMPORTPROGRESSAUTHPLACEHOLDER']._serialized_start=29141
  _globals['_GETIMPORTPROGRESSAUTHPLACEHOLDER']._serialized_end=29212
  _globals['_LISTIMPORTSAUTHPLACEHOLDER']._serialized_start=29214
  _globals['_LISTIMPORTSAUTHPLACEHOLDER']._serialized_end=29304
  _globals['_RUNANALYZERREQUEST']._serialized_start=29307
  _globals['_RUNANALYZERREQUEST']._serialized_end=29543
  _globals['_ANALYZERTOKEN']._serialized_start=29546
  _globals['_ANALYZERTOKEN']._serialized_end=29675
  _globals['_ANALYZERRESULT']._serialized_start=29677
  _globals['_ANALYZERRESULT']._serialized_end=29745
  _globals['_RUNANALYZERRESPONSE']._serialized_start=29747
  _globals['_RUNANALYZERRESPONSE']._serialized_end=29867
  _globals['_ADDUSERTAGSREQUEST']._serialized_start=29870
  _globals['_ADDUSERTAGSREQUEST']._serialized_end=30074
  _globals['_ADDUSERTAGSREQUEST_TAGSENTRY']._serialized_start=30020
  _globals['_ADDUSERTAGSREQUEST_TAGSENTRY']._serialized_end=30063
  _globals['_DELETEUSERTAGSREQUEST']._serialized_start=30076
  _globals['_DELETEUSERTAGSREQUEST']._serialized_end=30191
  _globals['_GETUSERTAGSREQUEST']._serialized_start=30193
  _globals['_GETUSERTAGSREQUEST']._serialized_end=30287
  _globals['_GETUSERTAGSRESPONSE']._serialized_start=30290
  _globals['_GETUSERTAGSRESPONSE']._serialized_end=30467
  _globals['_GETUSERTAGSRESPONSE_TAGSENTRY']._serialized_start=30020
  _globals['_GETUSERTAGSRESPONSE_TAGSENTRY']._serialized_end=30063
  _globals['_LISTUSERSWITHTAGREQUEST']._serialized_start=30469
  _globals['_LISTUSERSWITHTAGREQUEST']._serialized_end=30594
  _globals['_LISTUSERSWITHTAGRESPONSE']._serialized_start=30596
  _globals['_LISTUSERSWITHTAGRESPONSE']._serialized_end=30687
  _globals['_CREATEROWPOLICYREQUEST']._serialized_start=30690
  _globals['_CREATEROWPOLICYREQUEST']._serialized_end=30961
  _globals['_DROPROWPOLICYREQUEST']._serialized_start=30964
  _globals['_DROPROWPOLICYREQUEST']._serialized_end=31102
  _globals['_LISTROWPOLICIESREQUEST']._serialized_start=31104
  _globals['_LISTROWPOLICIESREQUEST']._serialized_end=31223
  _globals['_ROWPOLICY']._serialized_start=31226
  _globals['_ROWPOLICY']._serialized_end=31409
  _globals['_LISTROWPOLICIESRESPONSE']._serialized_start=31412
  _globals['_LISTROWPOLICIESRESPONSE']._serialized_end=31574
  _globals['_MILVUSSERVICE']._serialized_start=32064
  _globals['_MILVUSSERVICE']._serialized_end=42559
  _globals['_PROXYSERVICE']._serialized_start=42561
  _globals['_PROXYSERVICE']._serialized_end=42678
# @@protoc_insertion_point(module_scope)
