# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: msg.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'msg.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import common_pb2 as common__pb2
from . import schema_pb2 as schema__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\tmsg.proto\x12\x10milvus.proto.msg\x1a\x0c\x63ommon.proto\x1a\x0cschema.proto\"\xaa\x03\n\rInsertRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x11\n\tshardName\x18\x02 \x01(\t\x12\x0f\n\x07\x64\x62_name\x18\x03 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x04 \x01(\t\x12\x16\n\x0epartition_name\x18\x05 \x01(\t\x12\x0c\n\x04\x64\x62ID\x18\x06 \x01(\x03\x12\x14\n\x0c\x63ollectionID\x18\x07 \x01(\x03\x12\x13\n\x0bpartitionID\x18\x08 \x01(\x03\x12\x11\n\tsegmentID\x18\t \x01(\x03\x12\x12\n\ntimestamps\x18\n \x03(\x04\x12\x0e\n\x06rowIDs\x18\x0b \x03(\x03\x12+\n\x08row_data\x18\x0c \x03(\x0b\x32\x19.milvus.proto.common.Blob\x12\x33\n\x0b\x66ields_data\x18\r \x03(\x0b\x32\x1e.milvus.proto.schema.FieldData\x12\x10\n\x08num_rows\x18\x0e \x01(\x04\x12\x34\n\x07version\x18\x0f \x01(\x0e\x32#.milvus.proto.msg.InsertDataVersion\"\xcf\x02\n\rDeleteRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x11\n\tshardName\x18\x02 \x01(\t\x12\x0f\n\x07\x64\x62_name\x18\x03 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x04 \x01(\t\x12\x16\n\x0epartition_name\x18\x05 \x01(\t\x12\x0c\n\x04\x64\x62ID\x18\x06 \x01(\x03\x12\x14\n\x0c\x63ollectionID\x18\x07 \x01(\x03\x12\x13\n\x0bpartitionID\x18\x08 \x01(\x03\x12\x1a\n\x12int64_primary_keys\x18\t \x03(\x03\x12\x12\n\ntimestamps\x18\n \x03(\x04\x12\x10\n\x08num_rows\x18\x0b \x01(\x03\x12.\n\x0cprimary_keys\x18\x0c \x01(\x0b\x32\x18.milvus.proto.schema.IDs\x12\x12\n\nsegment_id\x18\r \x01(\x03\"W\n\x0bMsgPosition\x12\x14\n\x0c\x63hannel_name\x18\x01 \x01(\t\x12\r\n\x05msgID\x18\x02 \x01(\x0c\x12\x10\n\x08msgGroup\x18\x03 \x01(\t\x12\x11\n\ttimestamp\x18\x04 \x01(\x04\"\x9f\x02\n\x17\x43reateCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x16\n\x0e\x63ollectionName\x18\x03 \x01(\t\x12\x15\n\rpartitionName\x18\x04 \x01(\t\x12\x0c\n\x04\x64\x62ID\x18\x05 \x01(\x03\x12\x14\n\x0c\x63ollectionID\x18\x06 \x01(\x03\x12\x13\n\x0bpartitionID\x18\x07 \x01(\x03\x12\x0e\n\x06schema\x18\x08 \x01(\x0c\x12\x1b\n\x13virtualChannelNames\x18\t \x03(\t\x12\x1c\n\x14physicalChannelNames\x18\n \x03(\t\x12\x14\n\x0cpartitionIDs\x18\x0b \x03(\x03\"\x90\x01\n\x15\x44ropCollectionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x16\n\x0e\x63ollectionName\x18\x03 \x01(\t\x12\x0c\n\x04\x64\x62ID\x18\x04 \x01(\x03\x12\x14\n\x0c\x63ollectionID\x18\x05 \x01(\x03\"\xbf\x01\n\x16\x43reatePartitionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0epartition_name\x18\x04 \x01(\t\x12\x0c\n\x04\x64\x62ID\x18\x05 \x01(\x03\x12\x14\n\x0c\x63ollectionID\x18\x06 \x01(\x03\x12\x13\n\x0bpartitionID\x18\x07 \x01(\x03\"\xbd\x01\n\x14\x44ropPartitionRequest\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x16\n\x0epartition_name\x18\x04 \x01(\t\x12\x0c\n\x04\x64\x62ID\x18\x05 \x01(\x03\x12\x14\n\x0c\x63ollectionID\x18\x06 \x01(\x03\x12\x13\n\x0bpartitionID\x18\x07 \x01(\x03\"9\n\x0bTimeTickMsg\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\"\x9f\x01\n\rDataNodeTtMsg\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x14\n\x0c\x63hannel_name\x18\x02 \x01(\t\x12\x11\n\ttimestamp\x18\x03 \x01(\x04\x12\x39\n\x0esegments_stats\x18\x04 \x03(\x0b\x32!.milvus.proto.common.SegmentStats\"\x84\x01\n\x0cReplicateMsg\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0e\n\x06is_end\x18\x02 \x01(\x08\x12\x12\n\nis_cluster\x18\x03 \x01(\x08\x12\x10\n\x08\x64\x61tabase\x18\x04 \x01(\t\x12\x12\n\ncollection\x18\x05 \x01(\t\"\'\n\nImportFile\x12\n\n\x02id\x18\x01 \x01(\x03\x12\r\n\x05paths\x18\x02 \x03(\t\"\xeb\x02\n\tImportMsg\x12*\n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x1c.milvus.proto.common.MsgBase\x12\x0f\n\x07\x64\x62_name\x18\x02 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x03 \x01(\t\x12\x14\n\x0c\x63ollectionID\x18\x04 \x01(\x03\x12\x14\n\x0cpartitionIDs\x18\x05 \x03(\x03\x12\x39\n\x07options\x18\x06 \x03(\x0b\x32(.milvus.proto.msg.ImportMsg.OptionsEntry\x12+\n\x05\x66iles\x18\x07 \x03(\x0b\x32\x1c.milvus.proto.msg.ImportFile\x12\x35\n\x06schema\x18\x08 \x01(\x0b\x32%.milvus.proto.schema.CollectionSchema\x12\r\n\x05jobID\x18\t \x01(\x03\x1a.\n\x0cOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01*2\n\x11InsertDataVersion\x12\x0c\n\x08RowBased\x10\x00\x12\x0f\n\x0b\x43olumnBased\x10\x01\x42\x33Z1github.com/milvus-io/milvus-proto/go-api/v2/msgpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'msg_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z1github.com/milvus-io/milvus-proto/go-api/v2/msgpb'
  _globals['_IMPORTMSG_OPTIONSENTRY']._loaded_options = None
  _globals['_IMPORTMSG_OPTIONSENTRY']._serialized_options = b'8\001'
  _globals['_INSERTDATAVERSION']._serialized_start=2501
  _globals['_INSERTDATAVERSION']._serialized_end=2551
  _globals['_INSERTREQUEST']._serialized_start=60
  _globals['_INSERTREQUEST']._serialized_end=486
  _globals['_DELETEREQUEST']._serialized_start=489
  _globals['_DELETEREQUEST']._serialized_end=824
  _globals['_MSGPOSITION']._serialized_start=826
  _globals['_MSGPOSITION']._serialized_end=913
  _globals['_CREATECOLLECTIONREQUEST']._serialized_start=916
  _globals['_CREATECOLLECTIONREQUEST']._serialized_end=1203
  _globals['_DROPCOLLECTIONREQUEST']._serialized_start=1206
  _globals['_DROPCOLLECTIONREQUEST']._serialized_end=1350
  _globals['_CREATEPARTITIONREQUEST']._serialized_start=1353
  _globals['_CREATEPARTITIONREQUEST']._serialized_end=1544
  _globals['_DROPPARTITIONREQUEST']._serialized_start=1547
  _globals['_DROPPARTITIONREQUEST']._serialized_end=1736
  _globals['_TIMETICKMSG']._serialized_start=1738
  _globals['_TIMETICKMSG']._serialized_end=1795
  _globals['_DATANODETTMSG']._serialized_start=1798
  _globals['_DATANODETTMSG']._serialized_end=1957
  _globals['_REPLICATEMSG']._serialized_start=1960
  _globals['_REPLICATEMSG']._serialized_end=2092
  _globals['_IMPORTFILE']._serialized_start=2094
  _globals['_IMPORTFILE']._serialized_end=2133
  _globals['_IMPORTMSG']._serialized_start=2136
  _globals['_IMPORTMSG']._serialized_end=2499
  _globals['_IMPORTMSG_OPTIONSENTRY']._serialized_start=2453
  _globals['_IMPORTMSG_OPTIONSENTRY']._serialized_end=2499
# @@protoc_insertion_point(module_scope)
