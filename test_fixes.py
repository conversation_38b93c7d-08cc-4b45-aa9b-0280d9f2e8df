#!/usr/bin/env python3
"""
Test script to verify all the runtime error fixes are working correctly.
This script tests the configuration, logging, and basic service initialization.
"""

import os
import sys
import traceback
from pathlib import Path

def test_config_loading():
    """Test that configuration loads without errors"""
    print("Testing configuration loading...")
    try:
        from app.core.config import settings
        print(f"✓ Configuration loaded successfully")
        print(f"  - App Name: {settings.APP_NAME}")
        print(f"  - Environment: {settings.APP_ENV}")
        print(f"  - Milvus Host: {settings.MILVUS_HOST}")
        print(f"  - AskHR Collection: {settings.MILVUS_ASKHR_COLLECTION}")
        print(f"  - AskIT Collection: {settings.MILVUS_ASKIT_COLLECTION}")
        return True
    except Exception as e:
        print(f"✗ Configuration loading failed: {e}")
        traceback.print_exc()
        return False

def test_logs_directory():
    """Test that logs directory exists"""
    print("\nTesting logs directory...")
    logs_dir = Path("logs")
    if logs_dir.exists():
        print("✓ Logs directory exists")
        return True
    else:
        print("✗ Logs directory does not exist")
        return False

def test_milvus_service_import():
    """Test that MilvusService can be imported and initialized"""
    print("\nTesting MilvusService import...")
    try:
        from app.services.milvus_service import MilvusService
        service = MilvusService(collection_name="test_collection")
        print("✓ MilvusService imported and initialized successfully")
        return True
    except Exception as e:
        print(f"✗ MilvusService import/initialization failed: {e}")
        traceback.print_exc()
        return False

def test_embedding_model_import():
    """Test that sentence-transformers can be imported"""
    print("\nTesting sentence-transformers import...")
    try:
        from sentence_transformers import SentenceTransformer
        print("✓ sentence-transformers imported successfully")
        return True
    except ImportError as e:
        print(f"✗ sentence-transformers import failed: {e}")
        print("  This is expected if the package is not installed")
        return False
    except Exception as e:
        print(f"✗ Unexpected error importing sentence-transformers: {e}")
        return False

def test_fastapi_app_creation():
    """Test that FastAPI app can be created"""
    print("\nTesting FastAPI app creation...")
    try:
        from app.main import app
        print("✓ FastAPI app created successfully")
        print(f"  - Title: {app.title}")
        print(f"  - Version: {app.version}")
        return True
    except Exception as e:
        print(f"✗ FastAPI app creation failed: {e}")
        traceback.print_exc()
        return False

def test_query_request_validation():
    """Test QueryRequest validation"""
    print("\nTesting QueryRequest validation...")
    try:
        from app.api.v1.endpoints.query import QueryRequest
        
        # Test valid request
        valid_request = QueryRequest(question="What is the weather?")
        print("✓ Valid QueryRequest created successfully")
        
        # Test empty question
        try:
            invalid_request = QueryRequest(question="")
            print("✗ Empty question validation failed - should have raised error")
            return False
        except ValueError:
            print("✓ Empty question validation working correctly")
        
        # Test whitespace-only question
        try:
            invalid_request = QueryRequest(question="   ")
            print("✗ Whitespace-only question validation failed - should have raised error")
            return False
        except ValueError:
            print("✓ Whitespace-only question validation working correctly")
            
        return True
    except Exception as e:
        print(f"✗ QueryRequest validation test failed: {e}")
        traceback.print_exc()
        return False

def test_certificate_file():
    """Test certificate file existence"""
    print("\nTesting certificate file...")
    cert_path = Path("app/config/cert.crt")
    if cert_path.exists():
        print(f"✓ Certificate file exists at {cert_path}")
        return True
    else:
        print(f"✗ Certificate file not found at {cert_path}")
        return False

def main():
    """Run all tests"""
    print("Running runtime error fixes verification tests...\n")
    
    tests = [
        test_config_loading,
        test_logs_directory,
        test_certificate_file,
        test_milvus_service_import,
        test_embedding_model_import,
        test_fastapi_app_creation,
        test_query_request_validation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The runtime error fixes are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
